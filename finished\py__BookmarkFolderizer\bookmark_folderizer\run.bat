:: =============================================================================
:: Bookmark Folderizer - Main Runner
:: =============================================================================
@ECHO OFF
SETLOCAL ENABLEEXTENSIONS ENABLEDELAYEDEXPANSION
IF EXIST "%~1" (CD /D "%~1") ELSE (CD /D "%~dp0")

:: =============================================================================
:: UV: Check installation and run
:: =============================================================================
uv --version >NUL 2>&1
IF ERRORLEVEL 1 (
    ECHO ERROR: UV is not installed or not in PATH
    ECHO Please run uv_init.bat first to set up the environment
    PAUSE
    EXIT /B 1
)

:: =============================================================================
:: Execute main script with UV
:: =============================================================================
ECHO Running Bookmark Folderizer...
uv run python src/main.py %*

:: =============================================================================
:: Keep terminal open for review
:: =============================================================================
ECHO.
ECHO Press any key to exit...
PAUSE > NUL
