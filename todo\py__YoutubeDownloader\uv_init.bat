:: =============================================================================
:: UV Environment Initialization Script
:: =============================================================================
@ECHO OFF
SETLOCAL ENABLEEXTENSIONS ENABLEDELAYEDEXPANSION
IF EXIST "%~1" (CD /D "%~1") ELSE (CD /D "%~dp0")
SET "__init_path__=%CD%"

:: =============================================================================
:: Check for uv installation
:: =============================================================================
WHERE uv >nul 2>&1
IF ERRORLEVEL 1 (
    ECHO uv is not installed or not in PATH
    ECHO Please install uv: https://docs.astral.sh/uv/getting-started/installation/
    PAUSE>NUL & EXIT /B
)

:: =============================================================================
:: Initialize uv project
:: =============================================================================
IF NOT EXIST "pyproject.toml" (
    ECHO pyproject.toml not found. Please ensure it exists.
    PAUSE>NUL & EXIT /B
)

ECHO Initializing uv environment...
"uv" sync

IF ERRORLEVEL 1 (
    ECHO Failed to initialize uv environment
    PAUSE>NUL & EXIT /B
) ELSE (
    ECHO uv environment initialized successfully
)

ECHO.
ECHO Environment ready. Use 'uv run python main.py' to execute the application.
ECHO Window will close in 5 seconds...
PING 127.0.0.1 -n 5 > NUL
EXIT /B
