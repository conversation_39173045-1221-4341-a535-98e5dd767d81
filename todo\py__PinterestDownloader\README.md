# Pinterest Downloader

A Python-based Pinterest content downloader using gallery-dl, designed as an equivalent to the YoutubeDownloader but specifically for Pinterest images and videos.

## Features

- **Pinterest Support**: Download images and videos from Pinterest pins, boards, and user profiles
- **Multiple Download Options**: Choose between different quality settings and content types
- **Rich UI**: Beautiful command-line interface with progress bars and colored output
- **Concurrent Downloads**: Multiple downloads running simultaneously for faster processing
- **URL Validation**: Automatic validation of Pinterest URLs
- **Smart Clipboard Detection**: Automatically detects multiple Pinterest URLs from clipboard content and offers them as defaults
- **Robust Error Handling**: Terminal never closes unexpectedly, comprehensive error reporting with user guidance

## Supported Pinterest URLs

- Individual pins: `https://pinterest.com/pin/123456789/`
- Short URLs: `https://pin.it/abc123`
- Boards: `https://pinterest.com/username/board-name/`
- User profiles: `https://pinterest.com/username/`

## Download Options

1. **Images & Videos: Best Quality** - Downloads all content in highest available quality
2. **Images Only: High Quality** - Downloads only images, skipping videos
3. **Videos Only: Best Quality** - Downloads only videos, skipping images
4. **Images: Medium Quality (faster)** - Downloads images with limited range for faster processing
5. **Board/Profile: All Content** - Downloads up to 200 items from boards or profiles

## Installation

1. **Initialize Virtual Environment**:
   ```bash
   .\py_venv_init.bat
   ```

2. **Run the Application**:
   ```bash
   .\main.bat
   ```

## Smart Clipboard Detection

The Pinterest downloader features intelligent clipboard detection that automatically finds Pinterest URLs in your clipboard content:

### How It Works

1. **Automatic Detection**: When you run the downloader, it scans your clipboard for Pinterest URLs
2. **Multiple URL Support**: Finds and extracts multiple Pinterest URLs from mixed content
3. **Smart Filtering**: Removes duplicates and validates all detected URLs
4. **User-Friendly Display**: Shows all found URLs in a clear, numbered list
5. **Default Suggestion**: Offers detected URLs as defaults - just press Enter to use them

### Example Clipboard Content

If your clipboard contains:
```
Check out these Pinterest finds:
https://pinterest.com/pin/123456789/
And this board: https://pinterest.com/username/home-decor/
Don't miss: https://pin.it/abc123
```

The downloader will automatically detect all 3 URLs and display:
```
✅ Found 3 Pinterest URL(s) in clipboard:
  1. https://pinterest.com/pin/123456789/
  2. https://pinterest.com/username/home-decor/
  3. https://pin.it/abc123

Enter the Pinterest URL(s) [Press Enter to use clipboard URLs]:
```

## Usage

### Command Line Arguments

- `-i, --input_urls`: Pinterest URL(s) to download
- `-op, --output_path`: Output directory path
- `--prompt`: Force interactive prompts for all inputs

### Examples

**Interactive Mode** (recommended):
```bash
python src/main.py --prompt
```

**Command Line Mode**:
```bash
python src/main.py -i "https://pinterest.com/pin/123456789/" -op "C:\Downloads\Pinterest"
```

**Multiple URLs**:
```bash
python src/main.py -i "https://pin.it/abc123" "https://pinterest.com/username/board/" -op ".\downloads"
```

## Dependencies

- **gallery-dl**: Core Pinterest downloading functionality
- **rich**: Beautiful terminal UI and progress bars
- **loguru**: Advanced logging capabilities
- **pyperclip**: Clipboard integration for URL detection

## File Structure

```
py__PinterestDownloader/
├── src/
│   └── main.py              # Main application script
├── tests/
│   └── test_basic.py        # Test suite
├── downloads/               # Default download directory
├── venv/                    # Virtual environment
├── requirements.txt         # Python dependencies
├── main.bat                # Windows launcher script
├── py_venv_init.bat        # Virtual environment setup
├── demo.py                 # Demonstration script
└── README.md               # This documentation
```

## Comparison with YoutubeDownloader

| Feature | YoutubeDownloader | PinterestDownloader |
|---------|-------------------|---------------------|
| **Backend** | yt-dlp | gallery-dl |
| **Content Types** | Videos, Audio | Images, Videos |
| **URL Support** | YouTube URLs | Pinterest URLs |
| **Download Options** | Video quality, audio formats | Content type, quality filters |
| **UI/UX** | Rich progress bars | Rich progress bars |
| **Concurrency** | Yes | Yes |
| **Error Handling** | Comprehensive | Comprehensive |

## Error Handling & Terminal Persistence

The Pinterest downloader includes robust error handling to ensure the terminal never closes unexpectedly:

### 🛡️ **Error Recovery Features**

- **Terminal Persistence**: Always waits for user input before closing, even on errors
- **Graceful Error Handling**: All errors are caught and displayed with clear messages
- **User Guidance**: Error messages include helpful suggestions for resolution
- **Keyboard Interrupt Handling**: Ctrl+C is handled gracefully without crashes
- **Detailed Logging**: Errors are logged for debugging while showing user-friendly messages
- **No Silent Failures**: All errors are reported to the user with appropriate context

### 🔧 **Error Scenarios Handled**

1. **No URLs Provided**: Clear message with clipboard guidance
2. **Invalid Pinterest URLs**: Option to continue with valid URLs only
3. **Download Failures**: Detailed error reporting with summary
4. **Network Issues**: Graceful handling with retry suggestions
5. **Permission Errors**: Clear messages about file/directory access
6. **Dependency Issues**: Helpful messages about missing requirements

### ⌨️ **User Experience**

- **Never Crashes**: Terminal stays open for user to read error messages
- **Clear Messages**: No technical jargon, user-friendly error descriptions
- **Recovery Options**: Suggestions for fixing issues and trying again
- **Graceful Cancellation**: Ctrl+C shows clean cancellation message

## Troubleshooting

### Common Issues

1. **"gallery-dl not found"**: Ensure virtual environment is activated and dependencies are installed
2. **"Invalid Pinterest URL"**: Check URL format and ensure it's a valid Pinterest link
3. **Download failures**: Some Pinterest content may be private or restricted

### Getting Help

- Check the error messages in the terminal output
- Verify Pinterest URLs are accessible in a web browser
- Ensure you have sufficient disk space for downloads
- Error messages include specific guidance for resolution

## Technical Details

- **Language**: Python 3.8+
- **Architecture**: Modular design with separate functions for URL validation, downloading, and UI
- **Error Recovery**: Graceful handling of network errors and invalid URLs
- **Logging**: Detailed logging with automatic cleanup of empty log files
- **Progress Tracking**: Real-time progress updates for each download task

## License

This project follows the same license structure as the parent YoutubeDownloader project.
