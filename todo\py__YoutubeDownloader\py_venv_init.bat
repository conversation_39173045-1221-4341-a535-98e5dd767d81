:: =============================================================================
:: cmd: initialize
:: =============================================================================
@ECHO OFF
SETLOCAL ENABLEEXTENSIONS ENABLEDELAYEDEXPANSION
IF EXIST "%~1" (CD /D "%~1") ELSE (CD /D "%~dp0")
SET "__init_path__=%CD%"
SET "__base_name__=%~n0"
SET "__venv_name__=venv"


:: =============================================================================
:: py: version
:: =============================================================================
SET /A "py_count=0"
FOR /F "delims=" %%p IN ('WHERE python 2^>nul') DO (
    FOR /F "tokens=*" %%v IN ('"%%p" --version 2^>^&1 2^>nul') DO (
        ECHO %%v | FIND "Python " > NUL && (
            SET /A "py_count+=1"
            SET "py_version!py_count!=%%v"
            SET "python_!py_count!=%%p"
        )
    )
)
IF %py_count% EQU 0 (
    ECHO No valid Python installations found. & ECHO. & PAUSE>NUL & EXIT /B
) ELSE IF %py_count% EQU 1 (SET "py_path=%python_1%") ELSE (
    ECHO Choose Version: & FOR /L %%i IN (1,1,%py_count%) DO (
        ECHO - [%%i] !py_version%%i! - !python_%%i!
    )
    ECHO. & SET /P version="Choose the Python version to use (enter number): "
    CALL SET "py_path=%%python_!version!%%" & ECHO.
)


:: =============================================================================
:: venv: create/initialize/activate
:: =============================================================================
SET "__venv_stem__=%__init_path__%"
IF NOT EXIST "%__venv_stem__%\Scripts\python.exe" ("!py_path!" -m venv "%__venv_name__%")
CALL "%__venv_stem__%\%__venv_name__%\Scripts\activate"


:: =============================================================================
:: .gitignore: write
:: =============================================================================
SETLOCAL DISABLEDELAYEDEXPANSION
ECHO * > "%__venv_stem__%\%__venv_name__%\.gitignore"
ECHO !.gitignore >> "%__venv_stem__%\%__venv_name__%\.gitignore"
ENDLOCAL


:: =============================================================================
:: uv: install dependencies
:: =============================================================================
SET "pyproject_toml=%__venv_stem__%\pyproject.toml"
IF EXIST "%pyproject_toml%" (
    ECHO Installing dependencies with uv...
    "uv" sync
    IF ERRORLEVEL 1 (
        ECHO Failed to install dependencies from "%pyproject_toml%"
        ECHO Falling back to pip installation...
        SET "requirements_txt=%__venv_stem__%\requirements.txt"
        IF EXIST "%requirements_txt%" (
            "python" -m pip install --upgrade pip
            "python" -m pip install -r "%requirements_txt%"
        )
    ) ELSE (
        ECHO Dependencies installed successfully with uv
    )
) ELSE (
    ECHO pyproject.toml not found, checking for requirements.txt...
    SET "requirements_txt=%__venv_stem__%\requirements.txt"
    IF EXIST "%requirements_txt%" (
        "python" -m pip install --upgrade pip
        "python" -m pip install -r "%requirements_txt%"
    )
)


:: =============================================================================
:: cmd: exit
:: =============================================================================
ECHO. & ECHO Window will close in 10 seconds ...& PING 127.0.0.1 -n 10 > NUL
EXIT /B
