import os
import argparse
import pyperclip
import re
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.prompt import Prompt, Confirm
from rich.progress import (
    Progress,
    SpinnerColumn,
    TimeElapsedColumn,
    TextColumn,
    BarColumn,
    TaskProgressColumn,
)
from concurrent.futures import ThreadPoolExecutor, as_completed
import yt_dlp
from loguru import logger
import io
import sys
from pathlib import Path

console = Console()
DOWNLOAD_TEMPLATES = {
    1: {'name': 'Video (.mp4): Best Quality (uncompressed)', 'options': {}},
    2: {'name': 'Video (.mp4): Best Quality', 'options': {'format': 'bestvideo+bestaudio'}},
    3: {'name': 'Video (.mp4): Low Quality', 'options': {'format': 'bestvideo[height<=480]+bestaudio/best[height<=480]'}},
    4: {'name': 'Audio (.mp3): 320kbps', 'options': {'format': 'bestaudio', 'postprocessors': [{'key': 'FFmpegExtractAudio', 'preferredcodec': 'mp3', 'preferredquality': '0'}]}},
    5: {'name': 'Video (.mp4): Best Quality (Single Format)', 'options': {'format': 'best'}}
}
DEFAULT_DOWNLOAD_OPTS = {"outtmpl": "%(playlist_index)s-%(uploader)s-%(title)s.%(ext)s"}

def close_logger():
    """
    Remove all logger sinks, ensuring that any open file handles (e.g. for the log file)
    are properly closed.
    """
    logger.remove()

def clean_up_logs():
    log_file = "youtube_downloader.log"
    # Close the logger to release the file handle before cleaning up
    close_logger()
    if os.path.exists(log_file):
        try:
            os.remove(log_file)
            console.print("[bold green]Successfully cleaned up log file[/bold green]")
        except Exception as e:
            console.print(f"[bold red]Error cleaning up log file: {str(e)}[/bold red]")

def parse_arguments():
    parser = argparse.ArgumentParser(description="Download videos from YouTube using yt-dlp.")
    parser.add_argument("-i", "--input_urls", nargs="+", help="Input YouTube URL(s)")
    parser.add_argument("-op", "--output_path", type=str, help="Output directory path")
    parser.add_argument("--prompt", action="store_true", help="Prompt the user for input values")
    return parser.parse_args()

def is_valid_youtube_url(url):
    youtube_regex = re.compile(r'(https?://)?(www\.)?(youtube|youtu|youtube-nocookie)\.(com|be)/(watch\?v=|embed/|v/|.+\?v=)?([^&=%\?]{11})')
    return youtube_regex.match(url) is not None

def prompt_inputs(args):
    # Check clipboard for a YouTube URL
    clipboard_content = pyperclip.paste()
    default_url = ""
    if is_valid_youtube_url(clipboard_content):
        default_url = clipboard_content
        console.print(f"[bold green]YouTube URL found in clipboard: {default_url}[/bold green]")

    if args.prompt or not args.input_urls:
        args.input_urls = Prompt.ask(
            "Enter the YouTube URL(s) (space-separated)", default=default_url
        ).split()
    if not args.input_urls:
        console.print("[bold red]Error: No URLs provided. Exiting...[/bold red]")
        sys.exit(1)
    if args.prompt or not args.output_path:
        args.output_path = Prompt.ask(
            "Output directory path", default=args.output_path or os.getcwd()
        )
    download_type = select_download_type()
    return args.input_urls, args.output_path, download_type

def select_download_type():
    console.print("\n[bold blue]Select download type (default: Best Quality Video):[/bold blue]")
    for key, template in DOWNLOAD_TEMPLATES.items():
        console.print(f"{key}. {template['name']}")
    choice = Prompt.ask(
        "Enter the number of your choice",
        choices=[str(i) for i in DOWNLOAD_TEMPLATES.keys()],
        default="2",
    )
    return int(choice)

class CustomLogger:
    def __init__(self, log_file="youtube_downloader.log"):
        self.logger = logger
        self.logger.remove()  # Remove any previously added sinks
        self.logger.add(sys.stderr, level="INFO", format="{time} - {level} - {message}")
        self.logger.add(log_file, level="DEBUG", format="{time} - {level} - {message} - {extra}")

    def debug(self, msg):
        self.logger.debug(msg)

    def info(self, msg):
        self.logger.info(msg)

    def warning(self, msg):
        self.logger.warning(msg)

    def error(self, msg):
        self.logger.error(msg)

    def exception(self, msg):
        self.logger.exception(msg)

def download_video(link, output_dir, download_opts, progress, task_id):
    # Create a copy of download_opts to avoid thread conflicts
    local_opts = download_opts.copy()
    local_opts["outtmpl"] = os.path.join(str(output_dir), local_opts["outtmpl"])
    local_opts["logger"] = CustomLogger().logger

    def progress_hook(d):
        if d["status"] == "downloading":
            total = d.get("total_bytes") or d.get("total_bytes_estimate", 0)
            downloaded = d.get("downloaded_bytes", 0)
            if total > 0:
                progress.update(task_id, completed=downloaded, total=total)
        elif d["status"] == "finished":
            progress.update(task_id, completed=100, total=100)

    local_opts["progress_hooks"] = [progress_hook]
    with yt_dlp.YoutubeDL(local_opts) as ydl:
        try:
            logger.info(f"Starting download: {link}")
            ydl.download([link])
            logger.info(f"Download successful: {link}")
            return {"link": link, "status": "success", "output_dir": str(output_dir)}
        except Exception as e:
            logger.exception(f"Error downloading {link}: {str(e)}")
            return {"link": link, "status": "error", "error": str(e)}

def download_videos(links, output_dir, download_opts):
    summary, errors = [], []
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TaskProgressColumn(),
        TimeElapsedColumn(),
        console=console,
    ) as progress:
        overall_task = progress.add_task("[cyan]Overall progress", total=len(links))
        with ThreadPoolExecutor() as executor:
            future_to_task = {}
            for link in links:
                task_id = progress.add_task(f"[cyan]Downloading {link}", total=100)
                future = executor.submit(
                    download_video, link, output_dir, download_opts, progress, task_id
                )
                future_to_task[future] = task_id
            for future in as_completed(future_to_task):
                result = future.result()
                task_id = future_to_task[future]
                progress.update(task_id, completed=100)
                progress.update(overall_task, advance=1)
                if result["status"] == "success":
                    summary.append(result)
                else:
                    errors.append(result)
    return summary, errors

def print_summary(summary, errors):
    console.print("\n" + "="*60)
    console.print("[bold blue]📋 DOWNLOAD RESULTS SUMMARY[/bold blue]")
    console.print("="*60)

    # Show successful downloads
    if summary:
        console.print(f"\n[bold green]✅ SUCCESSFUL DOWNLOADS ({len(summary)}):[/bold green]")
        success_table = Table(show_header=True, header_style="bold green")
        success_table.add_column("URL", style="cyan", width=50)
        success_table.add_column("Status", style="green", width=10)
        success_table.add_column("Output Directory", style="dim", width=30)

        for item in summary:
            # Truncate long URLs for better display
            display_url = item["link"][:47] + "..." if len(item["link"]) > 50 else item["link"]
            success_table.add_row(display_url, "✅ Success", item["output_dir"])
        console.print(success_table)
    else:
        console.print(f"\n[bold yellow]⚠️  No successful downloads[/bold yellow]")

    # Show failed downloads
    if errors:
        console.print(f"\n[bold red]❌ FAILED DOWNLOADS ({len(errors)}):[/bold red]")
        error_table = Table(show_header=True, header_style="bold red")
        error_table.add_column("URL", style="cyan", width=50)
        error_table.add_column("Status", style="red", width=10)
        error_table.add_column("Error Message", style="dim", width=40)

        for error in errors:
            # Truncate long URLs and error messages for better display
            display_url = error["link"][:47] + "..." if len(error["link"]) > 50 else error["link"]
            error_msg = error["error"][:37] + "..." if len(error["error"]) > 40 else error["error"]
            error_table.add_row(display_url, "❌ Failed", error_msg)
        console.print(error_table)
    else:
        console.print(f"\n[bold green]✅ No failed downloads[/bold green]")

    # Summary statistics
    console.print("\n" + "="*60)
    console.print("[bold blue]📊 STATISTICS:[/bold blue]")
    total_processed = len(summary) + len(errors)
    success_rate = (len(summary) / total_processed * 100) if total_processed > 0 else 0

    stats_table = Table(show_header=False, box=None)
    stats_table.add_column("Metric", style="bold")
    stats_table.add_column("Value", style="bold")

    stats_table.add_row("Total URLs processed:", f"{total_processed}")
    stats_table.add_row("Successful downloads:", f"[green]{len(summary)}[/green]")
    stats_table.add_row("Failed downloads:", f"[red]{len(errors)}[/red]")
    stats_table.add_row("Success rate:", f"[{'green' if success_rate >= 80 else 'yellow' if success_rate >= 50 else 'red'}]{success_rate:.1f}%[/{'green' if success_rate >= 80 else 'yellow' if success_rate >= 50 else 'red'}]")

    console.print(stats_table)
    console.print("="*60)

def wait_for_user_exit():
    """Wait for user to press any key before exiting"""
    console.print(f"\n[bold cyan]Press any key to exit...[/bold cyan]")
    try:
        input()  # Wait for user input
    except KeyboardInterrupt:
        pass  # Handle Ctrl+C gracefully

def main():
    args = parse_arguments()
    links, output_path, download_type_choice = prompt_inputs(args)
    output_path = Path(output_path)
    output_path.mkdir(parents=True, exist_ok=True)
    download_opts = {
        **DEFAULT_DOWNLOAD_OPTS,
        **DOWNLOAD_TEMPLATES[download_type_choice]["options"],
    }
    try:
        summary, errors = download_videos(links, output_path, download_opts)
        print_summary(summary, errors)

        if not errors:
            clean_up_logs()

    except Exception as e:
        console.print(f"\n[bold red]Error:[/bold red] {e}\n")
    finally:
        console.print("\n[bold green]Finished processing downloads.[/bold green]")
        wait_for_user_exit()

if __name__ == "__main__":
    main()
