@ECHO OFF
SETLOCAL ENABLEEXTENSIONS ENABLEDELAYEDEXPANSION
:: =============================================================================
:: YouTube Downloader - Environment Setup
:: =============================================================================
IF EXIST "%~1" (CD /D "%~1") ELSE (CD /D "%~dp0")

ECHO ========================================
ECHO  YouTube Downloader - Environment Setup
ECHO ========================================
ECHO.

:: Check for uv installation
WHERE uv >nul 2>&1
IF ERRORLEVEL 1 (
    ECHO [ERROR] uv is not installed or not in PATH
    ECHO Please install uv: https://docs.astral.sh/uv/getting-started/installation/
    ECHO.
    PAUSE>NUL & EXIT /B
)

ECHO [INFO] uv found: 
uv --version
ECHO.

:: Check for pyproject.toml
IF NOT EXIST "pyproject.toml" (
    ECHO [ERROR] pyproject.toml not found
    ECHO Please ensure the project is properly configured
    PAUSE>NUL & EXIT /B
)

:: Initialize environment
ECHO [INFO] Initializing uv environment and installing dependencies...
uv sync

IF ERRORLEVEL 1 (
    ECHO [ERROR] Failed to initialize environment
    PAUSE>NUL & EXIT /B
) ELSE (
    ECHO [SUCCESS] Environment initialized successfully
)

ECHO.
ECHO [INFO] Environment ready! Use 'run.bat' to start the application.
ECHO Window will close in 5 seconds...
PING 127.0.0.1 -n 5 > NUL
EXIT /B
