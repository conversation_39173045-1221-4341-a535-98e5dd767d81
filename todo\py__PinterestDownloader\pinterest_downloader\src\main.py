import os
import argparse
import pyperclip
import re
import sys
import subprocess
from pathlib import Path
from rich.console import Console
from rich.table import Table
from rich.prompt import Prompt, Confirm
from rich.progress import (
    Progress,
    SpinnerColumn,
    TimeElapsedColumn,
    TextColumn,
    BarColumn,
    TaskProgressColumn,
)
from concurrent.futures import ThreadPoolExecutor, as_completed
from loguru import logger

console = Console()

# Pinterest-specific download templates
DOWNLOAD_TEMPLATES = {
    1: {'name': 'Images & Videos: Best Quality', 'options': {}},
    2: {'name': 'Images Only: High Quality', 'options': {'image-filter': 'extension not in ("mp4", "webm", "mov")'}},
    3: {'name': 'Videos Only: Best Quality', 'options': {'image-filter': 'extension in ("mp4", "webm", "mov")'}},
    4: {'name': 'Images: Medium Quality (faster)', 'options': {'image-filter': 'extension not in ("mp4", "webm", "mov")', 'image-range': '1-50'}},
    5: {'name': 'Board/Profile: All Content', 'options': {'image-range': '1-200'}},
}

# Default options for gallery-dl
DEFAULT_DOWNLOAD_OPTS = {
    'options': {}
}

class CustomLogger:
    """Custom logger to capture gallery-dl output"""
    def __init__(self):
        self.logger = logger

    def debug(self, msg):
        pass  # Suppress debug messages

    def info(self, msg):
        if "Downloading" in msg or "Downloaded" in msg:
            self.logger.info(msg)

    def warning(self, msg):
        self.logger.warning(msg)

    def error(self, msg):
        self.logger.error(msg)

def parse_arguments():
    parser = argparse.ArgumentParser(description="Download images and videos from Pinterest using gallery-dl.")
    parser.add_argument("-i", "--input_urls", nargs="+", help="Input Pinterest URL(s)")
    parser.add_argument("-op", "--output_path", type=str, help="Output directory path")
    parser.add_argument("--prompt", action="store_true", help="Prompt the user for input values")
    return parser.parse_args()

def is_valid_pinterest_url(url):
    """Validate Pinterest URLs with comprehensive pattern matching"""
    if not url or not isinstance(url, str):
        return False

    url = url.strip()

    # Common Pinterest URL patterns - comprehensive domain support
    pinterest_patterns = [
        # Individual pins: pinterest.com/pin/123456789/ (all domains)
        r'(https?://)?(www\.)?[a-z]{2,3}\.pinterest\.com/pin/\d+/?(\?.*)?$',
        r'(https?://)?(www\.)?pinterest\.[a-z]{2,3}/pin/\d+/?(\?.*)?$',
        r'(https?://)?(www\.)?pinterest\.com/pin/\d+/?(\?.*)?$',

        # User profiles: pinterest.com/username/ (all domains)
        r'(https?://)?(www\.)?[a-z]{2,3}\.pinterest\.com/[a-zA-Z0-9_.-]+/?(\?.*)?$',
        r'(https?://)?(www\.)?pinterest\.[a-z]{2,3}/[a-zA-Z0-9_.-]+/?(\?.*)?$',
        r'(https?://)?(www\.)?pinterest\.com/[a-zA-Z0-9_.-]+/?(\?.*)?$',

        # Boards: pinterest.com/username/boardname/ (all domains)
        r'(https?://)?(www\.)?[a-z]{2,3}\.pinterest\.com/[a-zA-Z0-9_.-]+/[a-zA-Z0-9_.-]+/?(\?.*)?$',
        r'(https?://)?(www\.)?pinterest\.[a-z]{2,3}/[a-zA-Z0-9_.-]+/[a-zA-Z0-9_.-]+/?(\?.*)?$',
        r'(https?://)?(www\.)?pinterest\.com/[a-zA-Z0-9_.-]+/[a-zA-Z0-9_.-]+/?(\?.*)?$',

        # Search results (all domains)
        r'(https?://)?(www\.)?[a-z]{2,3}\.pinterest\.com/search/.*',
        r'(https?://)?(www\.)?pinterest\.[a-z]{2,3}/search/.*',
        r'(https?://)?(www\.)?pinterest\.com/search/.*',

        # Ideas (all domains)
        r'(https?://)?(www\.)?[a-z]{2,3}\.pinterest\.com/ideas/.*',
        r'(https?://)?(www\.)?pinterest\.[a-z]{2,3}/ideas/.*',
        r'(https?://)?(www\.)?pinterest\.com/ideas/.*',
    ]

    return any(re.match(pattern, url, re.IGNORECASE) for pattern in pinterest_patterns)

def get_clipboard_url():
    """Check clipboard for Pinterest URL with enhanced detection"""
    try:
        clipboard_content = pyperclip.paste()
        if not clipboard_content:
            return ""

        # Clean up the clipboard content
        clipboard_content = clipboard_content.strip()

        # Check if it's a direct Pinterest URL
        if is_valid_pinterest_url(clipboard_content):
            console.print(f"[bold green]✅ Pinterest URL found in clipboard: {clipboard_content}[/bold green]")
            return clipboard_content

        # Check if clipboard contains multiple lines and try to find Pinterest URLs
        lines = clipboard_content.split('\n')
        pinterest_urls = []

        for line in lines:
            line = line.strip()
            if line and is_valid_pinterest_url(line):
                pinterest_urls.append(line)

        if pinterest_urls:
            if len(pinterest_urls) == 1:
                console.print(f"[bold green]✅ Pinterest URL found in clipboard: {pinterest_urls[0]}[/bold green]")
                return pinterest_urls[0]
            else:
                console.print(f"[bold green]✅ Found {len(pinterest_urls)} Pinterest URLs in clipboard[/bold green]")
                for i, url in enumerate(pinterest_urls[:3], 1):  # Show first 3
                    console.print(f"  {i}. {url}")
                if len(pinterest_urls) > 3:
                    console.print(f"  ... and {len(pinterest_urls) - 3} more")
                return ' '.join(pinterest_urls)

        # Check if clipboard contains text that might contain Pinterest URLs
        if 'pinterest.com' in clipboard_content.lower():
            # Try to extract URLs using regex
            url_pattern = r'https?://[^\s<>"\']+pinterest\.com[^\s<>"\']*'
            found_urls = re.findall(url_pattern, clipboard_content, re.IGNORECASE)

            valid_urls = [url for url in found_urls if is_valid_pinterest_url(url)]
            if valid_urls:
                if len(valid_urls) == 1:
                    console.print(f"[bold green]✅ Pinterest URL extracted from clipboard: {valid_urls[0]}[/bold green]")
                    return valid_urls[0]
                else:
                    console.print(f"[bold green]✅ Extracted {len(valid_urls)} Pinterest URLs from clipboard[/bold green]")
                    return ' '.join(valid_urls)
            else:
                console.print("[yellow]📋 Clipboard contains Pinterest-related content but no valid URLs detected[/yellow]")

    except Exception as e:
        console.print(f"[yellow]Warning: Could not access clipboard: {e}[/yellow]")

    return ""

def select_download_type():
    """Display download options and get user selection"""
    console.print("\n[bold cyan]Select download type:[/bold cyan]")
    table = Table(show_header=True, header_style="bold magenta")
    table.add_column("Option", style="dim", width=12)
    table.add_column("Description")
    
    for key, value in DOWNLOAD_TEMPLATES.items():
        table.add_row(str(key), value['name'])
    console.print(table)
    
    while True:
        try:
            choice = int(Prompt.ask("Enter your choice", default="2"))
            if choice in DOWNLOAD_TEMPLATES:
                return choice
            console.print("[bold red]Invalid choice. Please try again.[/bold red]")
        except ValueError:
            console.print("[bold red]Please enter a valid number.[/bold red]")

def get_user_inputs(args):
    """Get user inputs with interactive prompts"""
    default_url = get_clipboard_url()

    # Always prompt for URLs if not provided or if prompt flag is set
    if args.prompt or not args.input_urls:
        urls_input = Prompt.ask("Enter Pinterest URL(s) (space-separated)", default=default_url)
        args.input_urls = urls_input.split() if urls_input.strip() else []

    # Interactive loop for URLs if still empty
    while not args.input_urls:
        console.print("\n[bold cyan]📌 Pinterest URL Required[/bold cyan]")
        console.print("Please provide at least one Pinterest URL to download.")
        urls_input = Prompt.ask("Enter Pinterest URL(s) (space-separated)", default="")
        if urls_input.strip():
            args.input_urls = urls_input.split()
        else:
            if not Confirm.ask("Would you like to try again?", default=True):
                console.print("[red]Exiting...[/red]")
                sys.exit(1)

    # Always prompt for output path if not provided or if prompt flag is set
    if args.prompt or not args.output_path:
        args.output_path = Prompt.ask("Output directory", default=args.output_path or os.getcwd())

    download_type = select_download_type()
    return args.input_urls, args.output_path, download_type

def setup_logger():
    """Setup logging configuration"""
    logger.remove()
    logger.add(sys.stderr, level="INFO", format="{time} - {level} - {message}")
    logger.add("pinterest_downloader.log", level="DEBUG", format="{time} - {level} - {message}")

def close_logger():
    """
    Remove all logger sinks, ensuring that any open file handles (e.g. for the log file)
    are properly closed.
    """
    logger.remove()

def cleanup_logs():
    """Clean up log files after successful execution"""
    log_file = "pinterest_downloader.log"
    # Close the logger to release the file handle before cleaning up
    close_logger()
    if os.path.exists(log_file):
        try:
            os.remove(log_file)
            console.print("[bold green]Log file cleaned up[/bold green]")
        except Exception as e:
            console.print(f"[bold red]Error cleaning log: {str(e)}[/bold red]")

def sanitize_filename(text, max_length=100):
    """Sanitize text for use as filename"""
    if not text:
        return "untitled"

    # Remove or replace invalid characters
    import string
    valid_chars = "-_.() %s%s" % (string.ascii_letters, string.digits)
    sanitized = ''.join(c for c in text if c in valid_chars)

    # Replace multiple spaces with single space and strip
    sanitized = ' '.join(sanitized.split())

    # Truncate if too long
    if len(sanitized) > max_length:
        sanitized = sanitized[:max_length].rsplit(' ', 1)[0]

    return sanitized or "untitled"

def extract_metadata_and_download_UNUSED(url, output_dir, options, progress, task_id):
    """Extract metadata first, then download with custom filename"""
    import json
    import tempfile
    from datetime import datetime

    try:
        progress.update(task_id, description=f"[cyan]Extracting metadata for {url}")

        # First, extract metadata using gallery-dl
        metadata_cmd = ["gallery-dl", "--dump-json", url]
        metadata_result = subprocess.run(metadata_cmd, capture_output=True, text=True, check=True)

        # Parse metadata - handle different JSON formats
        metadata_text = metadata_result.stdout.strip()
        if not metadata_text:
            raise ValueError("No metadata extracted")

        # Try to parse as JSON - handle both single object and array formats
        try:
            # First try parsing as a single JSON object
            metadata = json.loads(metadata_text)
            if isinstance(metadata, list) and metadata:
                metadata = metadata[0]  # Take first item if it's an array
        except json.JSONDecodeError:
            # If that fails, try parsing line by line
            metadata_lines = [line for line in metadata_text.split('\n') if line.strip()]
            if not metadata_lines:
                raise ValueError("No valid metadata found")

            # Try to find a valid JSON line
            metadata = None
            for line in metadata_lines:
                try:
                    parsed = json.loads(line)
                    if isinstance(parsed, dict):
                        metadata = parsed
                        break
                except json.JSONDecodeError:
                    continue

            if metadata is None:
                raise ValueError("No valid JSON metadata found")

        # Extract title and date information
        title = metadata.get('title', '')
        description = metadata.get('description', '')
        pin_id = metadata.get('id', '')
        username = metadata.get('uploader', metadata.get('user', ''))

        # Use title, fallback to description, then to pin info
        if title:
            content_title = title
        elif description:
            content_title = description
        else:
            content_title = f"pin_{pin_id}" if pin_id else "pinterest_content"

        # Clean up title (remove hashtags, mentions, etc.)
        import re
        content_title = re.sub(r'[#@]\w+', '', content_title).strip()
        content_title = re.sub(r'\s+', ' ', content_title)  # Clean up multiple spaces

        # Sanitize for filename
        clean_title = sanitize_filename(content_title)

        # Get date - try different date fields
        date_str = metadata.get('date', metadata.get('created_at', ''))
        if date_str:
            try:
                # Parse date and format as YYYY.MM.DD
                if isinstance(date_str, str):
                    # Try different date formats
                    for fmt in ['%Y-%m-%dT%H:%M:%S', '%Y-%m-%d %H:%M:%S', '%Y-%m-%d']:
                        try:
                            date_obj = datetime.strptime(date_str.split('.')[0].split('+')[0], fmt)
                            break
                        except ValueError:
                            continue
                    else:
                        # If no format works, use current date
                        date_obj = datetime.now()
                else:
                    # If it's already a datetime object or timestamp
                    date_obj = datetime.fromtimestamp(date_str) if isinstance(date_str, (int, float)) else datetime.now()

                date_prefix = date_obj.strftime("%Y.%m.%d")
            except:
                date_prefix = datetime.now().strftime("%Y.%m.%d")
        else:
            date_prefix = datetime.now().strftime("%Y.%m.%d")

        # Create custom filename pattern
        if username:
            filename_base = f"{date_prefix}-{username}-{clean_title}"
        else:
            filename_base = f"{date_prefix}-{clean_title}"

        progress.update(task_id, description=f"[cyan]Downloading with custom filename")

        # Create a temporary config file for gallery-dl with custom filename
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as config_file:
            config = {
                "extractor": {
                    "pinterest": {
                        "filename": filename_base + ".{extension}"
                    }
                }
            }
            json.dump(config, config_file)
            config_path = config_file.name

        try:
            # Prepare gallery-dl command with custom config
            cmd = ["gallery-dl", "--config", config_path, "--dest", str(output_dir)]

            # Add options
            for key, value in options.items():
                if key == 'image-filter':
                    cmd.extend(["--filter", value])
                elif key == 'image-range':
                    cmd.extend(["--range", value])

            cmd.append(url)

            # Execute gallery-dl with custom filename
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)

            progress.update(task_id, completed=100, total=100)
            logger.info(f"Download successful: {url}")

            return {
                "url": url,
                "status": "success",
                "output_dir": str(output_dir),
                "title": clean_title,
                "username": username,
                "filename": filename_base
            }

        finally:
            # Clean up temporary config file
            try:
                os.unlink(config_path)
            except:
                pass

    except subprocess.CalledProcessError as e:
        logger.error(f"gallery-dl failed for {url}: {e}")
        progress.update(task_id, description=f"[red]Failed: {url}")
        return {"url": url, "status": "error", "error": str(e)}
    except Exception as e:
        logger.exception(f"Download failed for {url}: {str(e)}")
        progress.update(task_id, description=f"[red]Failed: {url}")
        return {"url": url, "status": "error", "error": str(e)}

def download_pinterest_content(url, output_dir, options, progress, task_id):
    """Download content from Pinterest URL using gallery-dl with date prefix"""
    from datetime import datetime

    try:
        progress.update(task_id, description=f"[cyan]Processing {url}")

        # Get current date for filename prefix
        date_prefix = datetime.now().strftime("%Y.%m.%d")

        # Prepare gallery-dl command with date prefix in filename
        cmd = ["gallery-dl", "--dest", str(output_dir)]
        cmd.extend(["-o", f"filename={date_prefix}-{{filename}}.{{extension}}"])

        # Add options
        for key, value in options.items():
            if key == 'image-filter':
                cmd.extend(["--filter", value])
            elif key == 'image-range':
                cmd.extend(["--range", value])

        cmd.append(url)

        progress.update(task_id, description=f"[cyan]Downloading from Pinterest")

        # Execute gallery-dl
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)

        progress.update(task_id, completed=100, total=100)
        logger.info(f"Download successful: {url}")

        return {
            "url": url,
            "status": "success",
            "output_dir": str(output_dir)
        }

    except subprocess.CalledProcessError as e:
        logger.error(f"gallery-dl failed for {url}: {e}")
        progress.update(task_id, description=f"[red]Failed: {url}")
        return {"url": url, "status": "error", "error": str(e)}
    except Exception as e:
        logger.exception(f"Download failed for {url}: {str(e)}")
        progress.update(task_id, description=f"[red]Failed: {url}")
        return {"url": url, "status": "error", "error": str(e)}

def download_contents(urls, output_dir, options):
    """Download multiple Pinterest contents with progress tracking"""
    results = []
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TaskProgressColumn(),
        TimeElapsedColumn(),
        console=console,
    ) as progress:
        overall_task = progress.add_task("[cyan]Overall progress", total=len(urls))
        
        for url in urls:
            task_id = progress.add_task(f"[cyan]Processing {url}", total=100)
            result = download_pinterest_content(url, output_dir, options, progress, task_id)
            results.append(result)
            progress.update(overall_task, advance=1)
            
            if result["status"] == "success":
                progress.update(task_id, description=f"[green]✓ Downloaded {result['url']}", completed=100)
            else:
                progress.update(task_id, description=f"[red]✗ Failed {result['url']}", completed=100)
    
    return results

def print_results(results):
    """Display download results summary"""
    console.print("\n" + "="*50)
    console.print("[bold green]📋 DOWNLOAD SUMMARY[/bold green]")
    console.print("="*50)
    
    successful = [r for r in results if r["status"] == "success"]
    failed = [r for r in results if r["status"] == "error"]
    
    if successful:
        console.print(f"\n[bold green]✓ Successfully downloaded {len(successful)} item(s):[/bold green]")
        for item in successful:
            console.print(f"  • {item['url']}")
            console.print(f"    → Saved to: {item['output_dir']}")
    
    if failed:
        console.print(f"\n[bold red]✗ Failed to download {len(failed)} item(s):[/bold red]")
        for item in failed:
            console.print(f"  • {item['url']}")
            console.print(f"    → Error: {item['error']}")
    
    console.print(f"\n[bold cyan]📊 Total processed: {len(results)} item(s)[/bold cyan]")
    console.print("="*50)

def wait_for_user_exit():
    """Wait for user to press any key before exiting"""
    console.print(f"\n[bold cyan]Press any key to exit...[/bold cyan]")
    try:
        input()
    except KeyboardInterrupt:
        pass

def main():
    """Main entry point"""
    args = parse_arguments()
    urls, output_path, download_type = get_user_inputs(args)
    
    output_dir = Path(output_path)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    setup_logger()
    download_options = DOWNLOAD_TEMPLATES[download_type]['options'].copy()
    
    try:
        results = download_contents(urls, output_dir, download_options)
        print_results(results)
        
        if all(r["status"] == "success" for r in results):
            cleanup_logs()
            
    except Exception as e:
        console.print(f"\n[bold red]Error:[/bold red] {e}")
        logger.exception(f"Main execution error: {e}")
    finally:
        console.print("\n[bold green]Finished processing downloads.[/bold green]")
        wait_for_user_exit()

if __name__ == "__main__":
    main()
