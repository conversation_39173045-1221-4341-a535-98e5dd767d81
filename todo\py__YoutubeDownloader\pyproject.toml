[project]
name = "youtube-downloader"
version = "1.0.0"
description = "YouTube video/audio downloader with rich CLI interface"
requires-python = ">=3.8"
dependencies = [
    "colorama==0.4.6",
    "loguru==0.7.3",
    "markdown-it-py==3.0.0",
    "mdurl==0.1.2",
    "Pygments==2.19.1",
    "pyperclip==1.9.0",
    "rich==14.0.0",
    "win32-setctime==1.2.0",
    "yt-dlp==2025.5.22",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.uv]
dev-dependencies = []
