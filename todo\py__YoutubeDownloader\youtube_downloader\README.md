# YouTube Downloader

## Overview
YouTube video/audio downloader with rich CLI interface, concurrent downloads, and progress tracking.

## Features
- **Multiple download formats**: Video (MP4) and audio (MP3) with quality options
- **Clipboard integration**: Auto-detects YouTube URLs from clipboard
- **Concurrent downloads**: Parallel processing for multiple URLs
- **Rich progress tracking**: Real-time download progress with visual indicators
- **Interactive CLI**: User-friendly prompts and selections
- **Structured logging**: Comprehensive logging with automatic cleanup

## Quick Start
Run `run.bat` to start the interactive downloader (handles environment setup automatically)

## Usage
```bash
# Interactive mode (recommended)
run.bat

# Direct command line usage
uv run python src/main.py --prompt
uv run python src/main.py -i "URL1" "URL2" -op "output/path"
```

## Additional Tools
- `upgrade.bat` - Upgrade yt-dlp to the latest version
- `install_ffmpeg.bat` - Install FFmpeg for audio conversion (if needed)

## Dependencies
Managed via `pyproject.toml` with uv package manager.
