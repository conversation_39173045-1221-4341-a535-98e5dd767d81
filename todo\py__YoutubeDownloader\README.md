# YouTube Downloader

## Overview
YouTube video/audio downloader with rich CLI interface, concurrent downloads, and progress tracking.

## Features
- **Multiple download formats**: Video (MP4) and audio (MP3) with quality options
- **Clipboard integration**: Auto-detects YouTube URLs from clipboard
- **Concurrent downloads**: Parallel processing for multiple URLs
- **Rich progress tracking**: Real-time download progress with visual indicators
- **Interactive CLI**: User-friendly prompts and selections
- **Structured logging**: Comprehensive logging with automatic cleanup

## Quick Start
1. Run `setup.bat` to initialize the environment
2. Run `run.bat` to start the interactive downloader

## Usage
```bash
# Interactive mode (recommended)
run.bat

# Direct command line usage
uv run python main.py --prompt
uv run python main.py -i "URL1" "URL2" -op "output/path"
```

## Scripts
- `setup.bat` - Initialize uv environment and dependencies
- `run.bat` - Start the interactive downloader
- `upgrade.bat` - Upgrade yt-dlp to latest version
- `install_ffmpeg.bat` - Install FFmpeg (required for audio conversion)

## Dependencies
Managed via `pyproject.toml` with uv package manager. See `techstack.md` for complete technology stack details.
