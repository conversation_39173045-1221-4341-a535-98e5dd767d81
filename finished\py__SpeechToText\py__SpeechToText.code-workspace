{"folders": [{"path": "."}], "settings": {"python.defaultInterpreterPath": "./speech_to_text/.venv/Scripts/python.exe", "python.terminal.activateEnvironment": true, "files.exclude": {"**/__pycache__": true, "**/*.pyc": true, "**/output": true, "**/logs": true}, "editor.rulers": [88], "python.formatting.provider": "black", "python.linting.enabled": true, "python.linting.pylintEnabled": false, "python.linting.flake8Enabled": true}, "extensions": {"recommendations": ["ms-python.python", "ms-python.black-formatter", "ms-python.flake8"]}}