@ECHO OFF
SETLOCAL ENABLEEXTENSIONS ENABLEDELAYEDEXPANSION
:: =============================================================================
:: YouTube Downloader - Package Upgrade
:: =============================================================================
IF EXIST "%~1" (CD /D "%~1") ELSE (CD /D "%~dp0")

ECHO ========================================
ECHO  YouTube Downloader - Package Upgrade
ECHO ========================================
ECHO.

:: Check for uv installation
WHERE uv >nul 2>&1
IF ERRORLEVEL 1 (
    ECHO [ERROR] uv is not installed or not in PATH
    ECHO Please install uv: https://docs.astral.sh/uv/getting-started/installation/
    PAUSE>NUL & EXIT /B
)

:: Show current packages
ECHO [INFO] Current packages:
uv pip list
ECHO.

:: Upgrade yt-dlp
ECHO [INFO] Upgrading yt-dlp...
uv add yt-dlp@latest

IF ERRORLEVEL 1 (
    ECHO [ERROR] Failed to upgrade yt-dlp
    PAUSE>NUL & EXIT /B
) ELSE (
    ECHO [SUCCESS] yt-dlp upgraded successfully
)

ECHO.
ECHO Press any key to exit...
PAUSE >NUL
EXIT /B
