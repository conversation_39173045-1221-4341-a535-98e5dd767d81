import os
import argparse
import pyperclip
import re
import sys
import subprocess
from pathlib import Path
from rich.console import Console
from rich.table import Table
from rich.prompt import Prompt, Confirm
from rich.progress import (
    Progress,
    SpinnerColumn,
    TimeElapsedColumn,
    TextColumn,
    BarColumn,
    TaskProgressColumn,
)
from concurrent.futures import ThreadPoolExecutor, as_completed
from loguru import logger

console = Console()

# Pinterest-specific download templates
DOWNLOAD_TEMPLATES = {
    1: {'name': 'Images & Videos: Best Quality', 'options': {}},
    2: {'name': 'Images Only: High Quality', 'options': {'image-filter': 'extension not in ("mp4", "webm", "mov")'}},
    3: {'name': 'Videos Only: Best Quality', 'options': {'image-filter': 'extension in ("mp4", "webm", "mov")'}},
    4: {'name': 'Images: Medium Quality (faster)', 'options': {'image-filter': 'extension not in ("mp4", "webm", "mov")', 'image-range': '1-50'}},
    5: {'name': 'Board/Profile: All Content', 'options': {'image-range': '1-200'}},
}

# Default options for gallery-dl
DEFAULT_DOWNLOAD_OPTS = {
    'options': {}
}

class CustomLogger:
    """Custom logger to capture gallery-dl output"""
    def __init__(self):
        self.logger = logger

    def debug(self, msg):
        pass  # Suppress debug messages

    def info(self, msg):
        if "Downloading" in msg or "Downloaded" in msg:
            self.logger.info(msg)

    def warning(self, msg):
        self.logger.warning(msg)

    def error(self, msg):
        self.logger.error(msg)

def parse_arguments():
    parser = argparse.ArgumentParser(description="Download images and videos from Pinterest using gallery-dl.")
    parser.add_argument("-i", "--input_urls", nargs="+", help="Input Pinterest URL(s)")
    parser.add_argument("-op", "--output_path", type=str, help="Output directory path")
    parser.add_argument("--prompt", action="store_true", help="Prompt the user for input values")
    return parser.parse_args()

def is_valid_pinterest_url(url):
    """Validate Pinterest URLs"""
    pinterest_patterns = [
        r'(https?://)?(www\.)?pinterest\.(com|co\.uk|ca|de|fr|it|es|com\.au|jp|kr|com\.mx|ru|se|dk|no|fi|pt|pl|nl|be|at|ch|ie|nz|in|com\.br)/(pin/|board/|[^/]+/)',
        r'(https?://)?pin\.it/[a-zA-Z0-9]+',
        r'(https?://)?(www\.)?pinterest\.(com|co\.uk|ca|de|fr|it|es|com\.au|jp|kr|com\.mx|ru|se|dk|no|fi|pt|pl|nl|be|at|ch|ie|nz|in|com\.br)/[^/]+/?'
    ]

    for pattern in pinterest_patterns:
        if re.match(pattern, url, re.IGNORECASE):
            return True
    return False

def get_pinterest_urls_from_clipboard():
    """Get Pinterest URLs from clipboard content"""
    try:
        clipboard_content = pyperclip.paste().strip()
        if not clipboard_content:
            return []

        # Split clipboard content by whitespace and newlines to find multiple URLs
        potential_urls = []
        for line in clipboard_content.split('\n'):
            for word in line.split():
                word = word.strip()
                if word and is_valid_pinterest_url(word):
                    potential_urls.append(word)

        # Remove duplicates while preserving order
        seen = set()
        unique_urls = []
        for url in potential_urls:
            if url not in seen:
                seen.add(url)
                unique_urls.append(url)

        return unique_urls
    except Exception:
        return []

def get_clipboard_content():
    """Get content from clipboard if it's a valid Pinterest URL (backward compatibility)"""
    urls = get_pinterest_urls_from_clipboard()
    return urls[0] if urls else ""

def select_download_type():
    """Display download options and get user selection"""
    table = Table(title="Pinterest Download Options", box=None)
    table.add_column("Option", style="cyan", no_wrap=True)
    table.add_column("Description", style="white")

    for key, template in DOWNLOAD_TEMPLATES.items():
        table.add_row(str(key), template['name'])

    console.print(table)

    while True:
        try:
            choice = int(Prompt.ask("Select download type", default="1"))
            if choice in DOWNLOAD_TEMPLATES:
                return choice
            else:
                console.print(f"[bold red]Invalid choice. Please select 1-{len(DOWNLOAD_TEMPLATES)}[/bold red]")
        except KeyboardInterrupt:
            raise  # Re-raise to be handled by caller
        except ValueError:
            console.print("[bold red]Please enter a valid number[/bold red]")
        except Exception as e:
            console.print(f"[bold red]Input error: {e}[/bold red]")
            console.print("[yellow]Please try again.[/yellow]")

def prompt_inputs(args):
    """Prompt user for inputs if not provided via command line"""
    # Check clipboard for Pinterest URLs
    clipboard_urls = get_pinterest_urls_from_clipboard()
    default_urls = " ".join(clipboard_urls) if clipboard_urls else ""

    # Display clipboard detection results
    if clipboard_urls:
        console.print(f"[bold green]✅ Found {len(clipboard_urls)} Pinterest URL(s) in clipboard:[/bold green]")
        for i, url in enumerate(clipboard_urls, 1):
            display_url = url if len(url) <= 60 else url[:57] + "..."
            console.print(f"  {i}. [cyan]{display_url}[/cyan]")
        console.print()

    if args.prompt or not args.input_urls:
        prompt_text = "Enter the Pinterest URL(s) (space-separated)"
        if clipboard_urls:
            prompt_text += f" [Press Enter to use clipboard URLs]"

        try:
            user_input = Prompt.ask(prompt_text, default=default_urls)
            args.input_urls = user_input.split() if user_input.strip() else []
        except KeyboardInterrupt:
            console.print("\n[yellow]Operation cancelled by user.[/yellow]")
            input("\nPress Enter to exit...")
            sys.exit(1)
        except Exception as e:
            console.print(f"\n[bold red]Input Error:[/bold red] {e}")
            console.print("[yellow]Please try again or check your terminal settings.[/yellow]")
            input("\nPress Enter to exit...")
            sys.exit(1)

    if not args.input_urls:
        console.print("[bold red]Error: No URLs provided.[/bold red]")
        console.print("[yellow]Please copy some Pinterest URLs to your clipboard and try again.[/yellow]")
        input("\nPress Enter to exit...")
        sys.exit(1)

    # Validate URLs
    invalid_urls = [url for url in args.input_urls if not is_valid_pinterest_url(url)]
    if invalid_urls:
        console.print(f"[bold red]Invalid Pinterest URLs detected:[/bold red]")
        for url in invalid_urls:
            console.print(f"[red]- {url}[/red]")
        try:
            if not Confirm.ask("Continue with valid URLs only?"):
                console.print("[yellow]Operation cancelled by user.[/yellow]")
                input("\nPress Enter to exit...")
                sys.exit(1)
        except KeyboardInterrupt:
            console.print("\n[yellow]Operation cancelled by user.[/yellow]")
            input("\nPress Enter to exit...")
            sys.exit(1)
        args.input_urls = [url for url in args.input_urls if is_valid_pinterest_url(url)]

    if args.prompt or not args.output_path:
        try:
            args.output_path = Prompt.ask(
                "Output directory path", default=args.output_path or os.getcwd()
            )
        except KeyboardInterrupt:
            console.print("\n[yellow]Operation cancelled by user.[/yellow]")
            input("\nPress Enter to exit...")
            sys.exit(1)
        except Exception as e:
            console.print(f"\n[bold red]Input Error:[/bold red] {e}")
            input("\nPress Enter to exit...")
            sys.exit(1)

    try:
        download_type = select_download_type()
    except KeyboardInterrupt:
        console.print("\n[yellow]Operation cancelled by user.[/yellow]")
        input("\nPress Enter to exit...")
        sys.exit(1)
    except Exception as e:
        console.print(f"\n[bold red]Selection Error:[/bold red] {e}")
        input("\nPress Enter to exit...")
        sys.exit(1)

    return args.input_urls, args.output_path, download_type

def clean_up_logs():
    """Clean up log files if no errors occurred"""
    try:
        log_files = ["pinterest-downloader.log"]
        for log_file in log_files:
            if os.path.exists(log_file) and os.path.getsize(log_file) == 0:
                os.remove(log_file)
    except Exception:
        pass

def download_content(link, output_dir, download_opts, progress, task_id):
    """Download content from Pinterest using gallery-dl"""
    try:
        # Monitor progress
        progress.update(task_id, description=f"[cyan]Processing {link[:50]}...")

        # Prepare gallery-dl command - use Python module execution to ensure virtual env compatibility
        cmd = [sys.executable, '-m', 'gallery_dl']

        # Add output directory
        if output_dir:
            cmd.extend(['--directory', str(output_dir)])

        # Add template-specific options
        template_opts = download_opts.get('options', {})
        for key, value in template_opts.items():
            if key == 'image-filter':
                cmd.extend(['--filter', str(value)])
            elif key == 'image-range':
                cmd.extend(['--range', str(value)])
            elif key.startswith('--'):
                cmd.append(key)
                if value and not isinstance(value, bool):
                    cmd.append(str(value))

        # Add URL
        cmd.append(link)

        # Debug: log the command being executed
        logger.info(f"Executing command: {' '.join(cmd)}")

        # Execute gallery-dl
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            universal_newlines=True
        )

        stdout, stderr = process.communicate()

        if process.returncode == 0:
            progress.update(task_id, completed=100, total=100, description="[green]✓ Completed")
            logger.info(f"Download successful: {link}")
            if stdout.strip():  # Log any useful output
                logger.debug(f"Gallery-dl output: {stdout.strip()}")
            return {"link": link, "status": "success", "output_dir": str(output_dir)}
        else:
            error_msg = stderr.strip() if stderr.strip() else "Unknown error occurred"
            logger.error(f"Error downloading {link}: {error_msg}")
            progress.update(task_id, description="[red]✗ Failed")
            return {"link": link, "status": "error", "error": error_msg}

    except Exception as e:
        logger.exception(f"Error downloading {link}: {str(e)}")
        progress.update(task_id, description="[red]✗ Failed")
        return {"link": link, "status": "error", "error": str(e)}

def download_multiple_contents(links, output_dir, download_opts):
    """Download multiple Pinterest contents concurrently"""
    summary, errors = [], []

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TaskProgressColumn(),
        TimeElapsedColumn(),
        console=console,
    ) as progress:
        overall_task = progress.add_task("[cyan]Overall progress", total=len(links))

        with ThreadPoolExecutor(max_workers=3) as executor:  # Limit concurrent downloads
            future_to_task = {}

            for link in links:
                task_id = progress.add_task(f"[cyan]Queued {link[:30]}...", total=100)
                future = executor.submit(
                    download_content, link, output_dir, download_opts, progress, task_id
                )
                future_to_task[future] = task_id

            for future in as_completed(future_to_task):
                result = future.result()
                task_id = future_to_task[future]

                if result['status'] == "success":
                    summary.append(result)
                    progress.update(task_id, description=f"[green]✓ Completed")
                else:
                    errors.append(result)
                    progress.update(task_id, description=f"[red]✗ Failed")

                progress.advance(overall_task)

    return summary, errors

def print_summary(summary, errors):
    """Print download summary"""
    console.print()

    if summary:
        success_table = Table(title="✅ Successful Downloads", box=None)
        success_table.add_column("URL", style="green")
        success_table.add_column("Output Directory", style="cyan")

        for item in summary:
            success_table.add_row(item['link'][:60] + "..." if len(item['link']) > 60 else item['link'],
                                item['output_dir'])

        console.print(success_table)

    if errors:
        console.print()
        error_table = Table(title="❌ Failed Downloads", box=None)
        error_table.add_column("URL", style="red")
        error_table.add_column("Error", style="yellow")

        for item in errors:
            error_table.add_row(item['link'][:60] + "..." if len(item['link']) > 60 else item['link'],
                              str(item['error'])[:80] + "..." if len(str(item['error'])) > 80 else str(item['error']))

        console.print(error_table)

    # Summary stats
    total = len(summary) + len(errors)
    console.print(f"\n[bold]Summary:[/bold] {len(summary)}/{total} successful downloads")

def main():
    """Main function"""
    try:
        args = parse_arguments()
        links, output_path, download_type_choice = prompt_inputs(args)

        output_path = Path(output_path)
        output_path.mkdir(parents=True, exist_ok=True)

        # Prepare download options
        download_opts = {
            **DEFAULT_DOWNLOAD_OPTS,
            **DOWNLOAD_TEMPLATES[download_type_choice]["options"],
        }

        try:
            summary, errors = download_multiple_contents(links, output_path, download_opts)
            print_summary(summary, errors)

            if not errors:
                clean_up_logs()

        except Exception as e:
            console.print(f"\n[bold red]Download Error:[/bold red] {e}")
            logger.exception("Download error occurred")

        console.print("\n[bold green]Finished processing downloads.[/bold green]")

    except KeyboardInterrupt:
        console.print("\n[yellow]Operation cancelled by user (Ctrl+C).[/yellow]")
    except Exception as e:
        console.print(f"\n[bold red]Unexpected Error:[/bold red] {e}")
        logger.exception("Unexpected error occurred")
        console.print("\n[yellow]Please check the error message above and try again.[/yellow]")
    finally:
        # Always wait for user input before closing
        try:
            input("\nPress Enter to exit...")
        except (KeyboardInterrupt, EOFError):
            pass  # Handle Ctrl+C or EOF gracefully

if __name__ == "__main__":
    main()
