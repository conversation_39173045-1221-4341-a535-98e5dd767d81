.TH "GALLERY-DL.CONF" "5" "2025-05-23" "1.29.7" "gallery-dl Manual"
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l

.SH NAME
gallery-dl.conf \- gallery-dl configuration file

.SH DESCRIPTION
gallery-dl will search for configuration files in the following places
every time it is started, unless
.B --ignore-config
is specified:
.PP
.RS 4
.nf
.I /etc/gallery-dl.conf
.I $HOME/.config/gallery-dl/config.json
.I $HOME/.gallery-dl.conf
.fi
.RE
.PP
It is also possible to specify additional configuration files with the
.B -c/--config
command-line option or to add further option values with
.B -o/--option
as <key>=<value> pairs,

Configuration files are JSON-based and therefore don't allow any ordinary
comments, but, since unused keys are simply ignored, it is possible to utilize
those as makeshift comments by settings their values to arbitrary strings.

.SH EXAMPLE
{
.RS 4
"base-directory": "/tmp/",
.br
"extractor": {
.RS 4
"pixiv": {
.RS 4
"directory": ["Pixiv", "Works", "{user[id]}"],
.br
"filename": "{id}{num}.{extension}",
.br
"username": "foo",
.br
"password": "bar"
.RE
},
.br
"flickr": {
.RS 4
"_comment": "OAuth keys for account 'foobar'",
.br
"access-token": "**********-**********abcdef",
.br
"access-token-secret": "fedcba9876543210"
.RE
}
.RE
},
.br
"downloader": {
.RS 4
"retries": 3,
.br
"timeout": 2.5
.RE
}
.RE
}

.SH EXTRACTOR OPTIONS
.SS extractor.*.filename
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]object\f[] (condition -> \f[I]format string\f[])

.IP "Example:" 4
.. code:: json

"{manga}_c{chapter}_{page:>03}.{extension}"

.. code:: json

{
"extension == 'mp4'": "{id}_video.{extension}",
"'nature' in title" : "{id}_{title}.{extension}",
""                  : "{id}_default.{extension}"
}


.IP "Description:" 4
A \f[I]format string\f[] to build filenames for downloaded files with.

If this is an \f[I]object\f[], it must contain Python expressions mapping to the
filename format strings to use.
These expressions are evaluated in the order as specified in Python 3.6+
and in an undetermined order in Python 3.4 and 3.5.

The available replacement keys depend on the extractor used. A list
of keys for a specific one can be acquired by calling *gallery-dl*
with the \f[I]-K\f[]/\f[I]--list-keywords\f[] command-line option.
For example:

.. code::

$ gallery-dl -K http://seiga.nicovideo.jp/seiga/im5977527
Keywords for directory names:

category
seiga
subcategory
image

Keywords for filenames:

category
seiga
extension
None
image-id
5977527
subcategory
image

Note: Even if the value of the \f[I]extension\f[] key is missing or
\f[I]None\f[], it will be filled in later when the file download is
starting. This key is therefore always available to provide
a valid filename extension.


.SS extractor.*.directory
.IP "Type:" 6
.br
* \f[I]list\f[] of \f[I]strings\f[]
.br
* \f[I]object\f[] (condition -> \f[I]format strings\f[])

.IP "Example:" 4
.. code:: json

["{category}", "{manga}", "c{chapter} - {title}"]

.. code:: json

{
"'nature' in content": ["Nature Pictures"],
"retweet_id != 0"    : ["{category}", "{user[name]}", "Retweets"],
""                   : ["{category}", "{user[name]}"]
}


.IP "Description:" 4
A list of \f[I]format strings\f[] to build target directory paths with.

If this is an \f[I]object\f[], it must contain Python expressions mapping to the
list of format strings to use.

Each individual string in such a list represents a single path
segment, which will be joined together and appended to the
\f[I]base-directory\f[] to form the complete target directory path.


.SS extractor.*.base-directory
.IP "Type:" 6
\f[I]Path\f[]

.IP "Default:" 9
\f[I]"./gallery-dl/"\f[]

.IP "Description:" 4
Directory path used as base for all download destinations.


.SS extractor.*.parent-directory
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Use an extractor's current target directory as
\f[I]base-directory\f[] for any spawned child extractors.


.SS extractor.*.metadata-parent
.IP "Type:" 6
.br
* \f[I]bool\f[]
.br
* \f[I]string\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
If \f[I]true\f[], overwrite any metadata provided by a child extractor
with its parent's.

If this is a \f[I]string\f[], add a parent's metadata to its children's
.br
to a field named after said string.
For example with \f[I]"parent-metadata": "_p_"\f[]:
.br

.. code:: json

{
"id": "child-id",
"_p_": {"id": "parent-id"}
}


.SS extractor.*.parent-skip
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Share number of skipped downloads between parent and child extractors.


.SS extractor.*.path-restrict
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]object\f[] (character -> replacement character(s))

.IP "Default:" 9
\f[I]"auto"\f[]

.IP "Example:" 4
.br
* "/!? (){}"
.br
* {" ": "_", "/": "-", "|": "-", ":": "_-_", "*": "_+_"}

.IP "Description:" 4
A string of characters to be replaced with the value of
.br
\f[I]path-replace\f[]
or an object mapping invalid/unwanted characters to their replacements
.br
for generated path segment names.
.br

Special values:

.br
* \f[I]"auto"\f[]: Use characters from \f[I]"unix"\f[] or \f[I]"windows"\f[]
depending on the local operating system
.br
* \f[I]"unix"\f[]: \f[I]"/"\f[]
.br
* \f[I]"windows"\f[]: \f[I]"\\\\\\\\|/<>:\\"?*"\f[]
.br
* \f[I]"ascii"\f[]: \f[I]"^0-9A-Za-z_."\f[] (only ASCII digits, letters, underscores, and dots)
.br
* \f[I]"ascii+"\f[]: \f[I]"^0-9@-[\\\\]-{ #-)+-.;=!}~"\f[] (all ASCII characters except the ones not allowed by Windows)

Implementation Detail: For \f[I]strings\f[] with length >= 2, this option uses a
\f[I]Regular Expression Character Set\f[],
meaning that:

.br
* using a caret \f[I]^\f[] as first character inverts the set
.br
* character ranges are supported (\f[I]0-9a-z\f[])
.br
* \f[I]]\f[], \f[I]-\f[], and \f[I]\\\f[] need to be escaped as
\f[I]\\\\]\f[], \f[I]\\\\-\f[], and \f[I]\\\\\\\\\f[] respectively
to use them as literal characters


.SS extractor.*.path-replace
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"_"\f[]

.IP "Description:" 4
The replacement character(s) for
\f[I]path-restrict\f[]


.SS extractor.*.path-remove
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"\\u0000-\\u001f\\u007f"\f[] (ASCII control characters)

.IP "Description:" 4
Set of characters to remove from generated path names.

Note: In a string with 2 or more characters, \f[I][]^-\\\f[] need to be
escaped with backslashes, e.g. \f[I]"\\\\[\\\\]"\f[]


.SS extractor.*.path-strip
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"auto"\f[]

.IP "Description:" 4
Set of characters to remove from the end of generated path segment names
using \f[I]str.rstrip()\f[]

Special values:

.br
* \f[I]"auto"\f[]: Use characters from \f[I]"unix"\f[] or \f[I]"windows"\f[]
depending on the local operating system
.br
* \f[I]"unix"\f[]: \f[I]""\f[]
.br
* \f[I]"windows"\f[]: \f[I]". "\f[]


.SS extractor.*.path-extended
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
On Windows, use \f[I]extended-length paths\f[]
prefixed with \f[I]\\\\?\\\f[] to work around the 260 characters path length limit.


.SS extractor.*.extension-map
.IP "Type:" 6
\f[I]object\f[] (extension -> replacement)

.IP "Default:" 9
.. code:: json

{
"jpeg": "jpg",
"jpe" : "jpg",
"jfif": "jpg",
"jif" : "jpg",
"jfi" : "jpg"
}

.IP "Description:" 4
A JSON \f[I]object\f[] mapping filename extensions to their replacements.


.SS extractor.*.skip
.IP "Type:" 6
.br
* \f[I]bool\f[]
.br
* \f[I]string\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Controls the behavior when downloading files that have been
downloaded before, i.e. a file with the same filename already
exists or its ID is in a \f[I]download archive\f[].

.br
* \f[I]true\f[]: Skip downloads
.br
* \f[I]false\f[]: Overwrite already existing files

.br
* \f[I]"abort"\f[]: Stop the current extractor run
.br
* \f[I]"abort:N"\f[]: Skip downloads and stop the current extractor run
after \f[I]N\f[] consecutive skips

.br
* \f[I]"terminate"\f[]: Stop the current extractor run, including parent extractors
.br
* \f[I]"terminate:N"\f[]: Skip downloads and stop the current extractor run,
including parent extractors, after \f[I]N\f[] consecutive skips

.br
* \f[I]"exit"\f[]: Exit the program altogether
.br
* \f[I]"exit:N"\f[]: Skip downloads and exit the program
after \f[I]N\f[] consecutive skips

.br
* \f[I]"enumerate"\f[]: Add an enumeration index to the beginning of the
filename extension (\f[I]file.1.ext\f[], \f[I]file.2.ext\f[], etc.)


.SS extractor.*.skip-filter
.IP "Type:" 6
\f[I]string\f[]

.IP "Description:" 4
Python expression controlling which skipped files to count towards
\f[I]"abort"\f[] / \f[I]"terminate"\f[] / \f[I]"exit"\f[].


.SS extractor.*.sleep
.IP "Type:" 6
\f[I]Duration\f[]

.IP "Default:" 9
\f[I]0\f[]

.IP "Description:" 4
Number of seconds to sleep before each download.


.SS extractor.*.sleep-extractor
.IP "Type:" 6
\f[I]Duration\f[]

.IP "Default:" 9
\f[I]0\f[]

.IP "Description:" 4
Number of seconds to sleep before handling an input URL,
i.e. before starting a new extractor.


.SS extractor.*.sleep-429
.IP "Type:" 6
\f[I]Duration\f[]

.IP "Default:" 9
\f[I]60\f[]

.IP "Description:" 4
Number of seconds to sleep when receiving a 429 Too Many Requests
response before \f[I]retrying\f[] the request.


.SS extractor.*.sleep-request
.IP "Type:" 6
\f[I]Duration\f[]

.IP "Default:" 9
.br
* \f[I]"0.5-1.5"\f[]
\f[I]ao3\f[],
\f[I]arcalive\f[],
\f[I]civitai\f[],
\f[I][Danbooru]\f[],
\f[I][E621]\f[],
\f[I][foolfuuka]:search\f[],
\f[I]itaku\f[],
\f[I]koharu\f[],
\f[I]newgrounds\f[],
\f[I][philomena]\f[],
\f[I]pixiv:novel\f[],
\f[I]plurk\f[],
\f[I]poipiku\f[] ,
\f[I]pornpics\f[],
\f[I]scrolller\f[],
\f[I]soundgasm\f[],
\f[I]urlgalleries\f[],
\f[I]vk\f[],
\f[I]webtoons\f[],
\f[I]weebcentral\f[],
\f[I]xfolio\f[],
\f[I]zerochan\f[]
.br
* \f[I]"1.0"\f[]
\f[I]furaffinity\f[]
.br
* \f[I]"1.0-2.0"\f[]
\f[I]flickr\f[],
\f[I]pexels\f[],
\f[I]weibo\f[],
\f[I][wikimedia]\f[]
.br
* \f[I]"1.4"\f[]
\f[I]wallhaven\f[]
.br
* \f[I]"2.0-4.0"\f[]
\f[I]behance\f[],
\f[I]imagefap\f[],
\f[I][Nijie]\f[]
.br
* \f[I]"3.0-6.0"\f[]
\f[I]bilibili\f[],
\f[I]exhentai\f[],
\f[I]idolcomplex\f[],
\f[I][reactor]\f[],
\f[I]readcomiconline\f[]
.br
* \f[I]"6.0-6.1"\f[]
\f[I]twibooru\f[]
.br
* \f[I]"6.0-12.0"\f[]
\f[I]instagram\f[]
.br
* \f[I]0\f[]
otherwise

.IP "Description:" 4
Minimal time interval in seconds between each HTTP request
during data extraction.


.SS extractor.*.username & .password
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]null\f[]

.IP "Description:" 4
The username and password to use when attempting to log in to
another site.

This is supported for

.br
* \f[I]aibooru\f[] (*)
.br
* \f[I]ao3\f[]
.br
* \f[I]aryion\f[]
.br
* \f[I]atfbooru\f[] (*)
.br
* \f[I]bluesky\f[]
.br
* \f[I]booruvar\f[] (*)
.br
* \f[I]coomerparty\f[]
.br
* \f[I]danbooru\f[] (*)
.br
* \f[I]deviantart\f[]
.br
* \f[I]e621\f[] (*)
.br
* \f[I]e6ai\f[] (*)
.br
* \f[I]e926\f[] (*)
.br
* \f[I]exhentai\f[]
.br
* \f[I]horne\f[] (R)
.br
* \f[I]idolcomplex\f[]
.br
* \f[I]imgbb\f[]
.br
* \f[I]inkbunny\f[]
.br
* \f[I]kemonoparty\f[]
.br
* \f[I]koharu\f[]
.br
* \f[I]mangadex\f[]
.br
* \f[I]mangoxo\f[]
.br
* \f[I]newgrounds\f[]
.br
* \f[I]nijie\f[] (R)
.br
* \f[I]pillowfort\f[]
.br
* \f[I]sankaku\f[]
.br
* \f[I]scrolller\f[]
.br
* \f[I]seiga\f[]
.br
* \f[I]subscribestar\f[]
.br
* \f[I]tapas\f[]
.br
* \f[I]tsumino\f[]
.br
* \f[I]twitter\f[]
.br
* \f[I]vipergirls\f[]
.br
* \f[I]zerochan\f[]

These values can also be specified via the
\f[I]-u/--username\f[] and \f[I]-p/--password\f[] command-line options or
by using a \f[I].netrc\f[] file. (see Authentication_)

(*) The password value for these sites should be
the API key found in your user profile, not the actual account password.

(R) Login with username & password or supplying logged-in
\f[I]cookies\f[] is required

Note: Leave the \f[I]password\f[] value empty or undefined
to be prompted for a password when performing a login
(see \f[I]getpass()\f[]).


.SS extractor.*.input
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[] if stdin is attached to a terminal,
\f[I]false\f[] otherwise

.IP "Description:" 4
Allow prompting the user for interactive input.


.SS extractor.*.netrc
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Enable the use of \f[I].netrc\f[] authentication data.


.SS extractor.*.cookies
.IP "Type:" 6
.br
* \f[I]Path\f[]
.br
* \f[I]object\f[] (name -> value)
.br
* \f[I]list\f[]

.IP "Description:" 4
Source to read additional cookies from. This can be

.br
* The \f[I]Path\f[] to a Mozilla/Netscape format cookies.txt file

.. code:: json

"~/.local/share/cookies-instagram-com.txt"

.br
* An \f[I]object\f[] specifying cookies as name-value pairs

.. code:: json

{
"cookie-name": "cookie-value",
"sessionid"  : "14313336321%3AsabDFvuASDnlpb%3A31",
"isAdult"    : "1"
}

.br
* A \f[I]list\f[] with up to 5 entries specifying a browser profile.

.br
* The first entry is the browser name
.br
* The optional second entry is a profile name or an absolute path to a profile directory
.br
* The optional third entry is the keyring to retrieve passwords for decrypting cookies from
.br
* The optional fourth entry is a (Firefox) container name (\f[I]"none"\f[] for only cookies with no container (default))
.br
* The optional fifth entry is the domain to extract cookies for. Prefix it with a dot \f[I].\f[] to include cookies for subdomains.

.. code:: json

["firefox"]
["firefox", null, null, "Personal"]
["chromium", "Private", "kwallet", null, ".twitter.com"]


.SS extractor.*.cookies-select
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"random"\f[]

.IP "Description:" 4
Interpret \f[I]extractor.cookies\f[]
as a list of cookie sources and select one of them for each extractor run.

.br
* \f[I]"random"\f[]: Select cookies \f[I]randomly\f[]
.br
* \f[I]"rotate"\f[]: Select cookies in sequence. Start over from the beginning after reaching the end of the list.

.. code:: json

[
"~/.local/share/cookies-instagram-com-1.txt",
"~/.local/share/cookies-instagram-com-2.txt",
"~/.local/share/cookies-instagram-com-3.txt",
["firefox", null, null, "c1", ".instagram-com"],
]


.SS extractor.*.cookies-update
.IP "Type:" 6
.br
* \f[I]bool\f[]
.br
* \f[I]Path\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Export session cookies in cookies.txt format.

.br
* If this is a \f[I]Path\f[], write cookies to the given file path.

.br
* If this is \f[I]true\f[] and \f[I]extractor.*.cookies\f[] specifies the \f[I]Path\f[]
of a valid cookies.txt file, update its contents.


.SS extractor.*.proxy
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]object\f[] (scheme -> proxy)

.IP "Example:" 4
.. code:: json

"http://**********:3128"

.. code:: json

{
"http" : "http://**********:3128",
"https": "http://**********:1080",
"http://***********": "http://**********:5323"
}


.IP "Description:" 4
Proxy (or proxies) to be used for remote connections.

.br
* If this is a \f[I]string\f[], it is the proxy URL for all
outgoing requests.
.br
* If this is an \f[I]object\f[], it is a scheme-to-proxy mapping to
specify different proxy URLs for each scheme.
It is also possible to set a proxy for a specific host by using
\f[I]scheme://host\f[] as key.
See \f[I]Requests' proxy documentation\f[] for more details.

Note: If a proxy URL does not include a scheme,
\f[I]http://\f[] is assumed.


.SS extractor.*.proxy-env
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Collect proxy configuration information from environment variables
(\f[I]HTTP_PROXY\f[], \f[I]HTTPS_PROXY\f[], \f[I]NO_PROXY\f[])
and Windows Registry settings.


.SS extractor.*.source-address
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]list\f[] with 1 \f[I]string\f[] and 1 \f[I]integer\f[] as elements

.IP "Example:" 4
.br
* "**************"
.br
* ["**************", 8080]

.IP "Description:" 4
Client-side IP address to bind to.

Can be either a simple \f[I]string\f[] with just the local IP address
.br
or a \f[I]list\f[] with IP and explicit port number as elements.
.br


.SS extractor.*.user-agent
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
.br
* \f[I]"gallery-dl/VERSION"\f[]: \f[I][Danbooru]\f[], \f[I]mangadex\f[], \f[I]weasyl\f[]
.br
* \f[I]"gallery-dl/VERSION (by mikf)"\f[]: \f[I][E621]\f[]
.br
* \f[I]"net.umanle.arca.android.playstore/0.9.75"\f[]: \f[I]arcalive\f[]
.br
* \f[I]"Patreon/72.2.28 (Android; Android 14; Scale/2.10)"\f[]: \f[I]patreon\f[]
.br
* \f[I]"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/LATEST.0.0.0 Safari/537.36"\f[]: \f[I]instagram\f[]
.br
* \f[I]"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:LATEST) Gecko/20100101 Firefox/LATEST"\f[]: otherwise

.IP "Description:" 4
User-Agent header value used for HTTP requests.

Setting this value to \f[I]"browser"\f[] will try to automatically detect
and use the \f[I]User-Agent\f[] header of the system's default browser.

Note:
This option has *no* effect if
\f[I]extractor.browser\f[]
is enabled.


.SS extractor.*.browser
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
.br
* \f[I]"firefox"\f[]: \f[I]artstation\f[], \f[I]fanbox\f[], \f[I]mangasee\f[], \f[I]twitter\f[]
.br
* \f[I]null\f[]: otherwise

.IP "Example:" 4
.br
* "chrome:macos"

.IP "Description:" 4
Try to emulate a real browser (\f[I]firefox\f[] or \f[I]chrome\f[])
by using their default HTTP headers and TLS ciphers for HTTP requests.

Optionally, the operating system used in the \f[I]User-Agent\f[] header can be
specified after a \f[I]:\f[] (\f[I]windows\f[], \f[I]linux\f[], or \f[I]macos\f[]).

Note:
This option overrides
\f[I]user-agent\f[]
and sets custom
\f[I]headers\f[]
and
\f[I]ciphers\f[]
defaults.

Note: \f[I]requests\f[] and \f[I]urllib3\f[] only support HTTP/1.1, while a real
browser would use HTTP/2.


.SS extractor.*.referer
.IP "Type:" 6
.br
* \f[I]bool\f[]
.br
* \f[I]string\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Send \f[I]Referer\f[]
headers with all outgoing HTTP requests.

If this is a \f[I]string\f[], send it as Referer
instead of the extractor's \f[I]root\f[] domain.


.SS extractor.*.headers
.IP "Type:" 6
\f[I]object\f[] (name -> value)

.IP "Default:" 9
.. code:: json

{
"User-Agent"     : "<extractor.*.user-agent>",
"Accept"         : "*/*",
"Accept-Language": "en-US,en;q=0.5",
"Accept-Encoding": "gzip, deflate",
"Referer"        : "<extractor.*.referer>"
}


.IP "Description:" 4
Additional \f[I]HTTP headers\f[]
to be sent with each HTTP request,

To disable sending a header, set its value to \f[I]null\f[].


.SS extractor.*.ciphers
.IP "Type:" 6
\f[I]list\f[] of \f[I]strings\f[]

.IP "Example:" 4
.. code:: json

["ECDHE-ECDSA-AES128-GCM-SHA256",
"ECDHE-RSA-AES128-GCM-SHA256",
"ECDHE-ECDSA-CHACHA20-POLY1305",
"ECDHE-RSA-CHACHA20-POLY1305"]


.IP "Description:" 4
List of TLS/SSL cipher suites in
\f[I]OpenSSL cipher list format\f[]
to be passed to
\f[I]ssl.SSLContext.set_ciphers()\f[]


.SS extractor.*.tls12
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
.br
* \f[I]false\f[]: \f[I]artstation\f[]
.br
* \f[I]true\f[]: otherwise

.IP "Description:" 4
Allow selecting TLS 1.2 cipher suites.

Can be disabled to alter TLS fingerprints
and potentially bypass Cloudflare blocks.


.SS extractor.*.keywords
.IP "Type:" 6
\f[I]object\f[] (name -> value)

.IP "Example:" 4
{"type": "Pixel Art", "type_id": 123}

.IP "Description:" 4
Additional name-value pairs to be added to each metadata dictionary.


.SS extractor.*.keywords-eval
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Evaluate each \f[I]keywords\f[] \f[I]string\f[] value
as a \f[I]format string\f[].


.SS extractor.*.keywords-default
.IP "Type:" 6
any

.IP "Default:" 9
\f[I]"None"\f[]

.IP "Description:" 4
Default value used for missing or undefined keyword names in
\f[I]format strings\f[].


.SS extractor.*.url-metadata
.IP "Type:" 6
\f[I]string\f[]

.IP "Description:" 4
Insert a file's download URL into its metadata dictionary as the given name.

For example, setting this option to \f[I]"gdl_file_url"\f[] will cause a new
metadata field with name \f[I]gdl_file_url\f[] to appear, which contains the
current file's download URL.
This can then be used in \f[I]filenames\f[],
with a \f[I]metadata\f[] post processor, etc.


.SS extractor.*.path-metadata
.IP "Type:" 6
\f[I]string\f[]

.IP "Description:" 4
Insert a reference to the current
\f[I]PathFormat\f[]
data structure into metadata dictionaries as the given name.

For example, setting this option to \f[I]"gdl_path"\f[] would make it possible
to access the current file's filename as \f[I]"{gdl_path.filename}"\f[].


.SS extractor.*.extractor-metadata
.IP "Type:" 6
\f[I]string\f[]

.IP "Description:" 4
Insert a reference to the current
\f[I]Extractor\f[]
object into metadata dictionaries as the given name.


.SS extractor.*.http-metadata
.IP "Type:" 6
\f[I]string\f[]

.IP "Description:" 4
Insert an \f[I]object\f[] containing a file's HTTP headers and
\f[I]filename\f[], \f[I]extension\f[], and \f[I]date\f[] parsed from them
into metadata dictionaries as the given name.

For example, setting this option to \f[I]"gdl_http"\f[] would make it possible
to access the current file's \f[I]Last-Modified\f[] header as \f[I]"{gdl_http[Last-Modified]}"\f[]
and its parsed form as \f[I]"{gdl_http[date]}"\f[].


.SS extractor.*.version-metadata
.IP "Type:" 6
\f[I]string\f[]

.IP "Description:" 4
Insert an \f[I]object\f[] containing gallery-dl's version info into
metadata dictionaries as the given name.

The content of the object is as follows:

.. code:: json

{
"version"         : "string",
"is_executable"   : "bool",
"current_git_head": "string or null"
}


.SS extractor.*.category-transfer
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
Extractor-specific

.IP "Description:" 4
Transfer an extractor's (sub)category values to all child
extractors spawned by it, to let them inherit their parent's
config options.


.SS extractor.*.blacklist & .whitelist
.IP "Type:" 6
\f[I]list\f[] of \f[I]strings\f[]

.IP "Default:" 9
\f[I]["oauth", "recursive", "test"]\f[] + current extractor category

.IP "Example:" 4
["imgur", "redgifs:user", "*:image"]

.IP "Description:" 4
A list of extractor identifiers to ignore (or allow)
when spawning child extractors for unknown URLs,
e.g. from \f[I]reddit\f[] or \f[I]plurk\f[].

Each identifier can be

.br
* A category or basecategory name (\f[I]"imgur"\f[], \f[I]"mastodon"\f[])
.br
* | A (base)category-subcategory pair, where both names are separated by a colon (\f[I]"redgifs:user"\f[]).
Both names can be a * or left empty, matching all possible names (\f[I]"*:image"\f[], \f[I]":user"\f[]).
.br

Note: Any \f[I]blacklist\f[] setting will automatically include
\f[I]"oauth"\f[], \f[I]"recursive"\f[], and \f[I]"test"\f[].


.SS extractor.*.archive
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]Path\f[]

.IP "Default:" 9
\f[I]null\f[]

.IP "Example:" 4
.br
* "$HOME/.archives/{category}.sqlite3"
.br
* "************************************"

.IP "Description:" 4
File to store IDs of downloaded files in. Downloads of files
already recorded in this archive file will be
\f[I]skipped\f[].

The resulting archive file is not a plain text file but an SQLite3
database, as either lookup operations are significantly faster or
memory requirements are significantly lower when the
amount of stored IDs gets reasonably large.

If this value is a
\f[I]PostgreSQL Connection URI\f[],
the archive will use this PostgreSQL database as backend (requires
\f[I]Psycopg\f[]).

Note: Archive files that do not already exist get generated automatically.

Note: Archive paths support regular \f[I]format string\f[] replacements,
but be aware that using external inputs for building local paths
may pose a security risk.


.SS extractor.*.archive-event
.IP "Type:" 6
+ \f[I]string\f[]
+ \f[I]list\f[] of \f[I]strings\f[]

.IP "Default:" 9
\f[I]"file"\f[]

.IP "Example:" 4
.br
* "file,skip"
.br
* ["file", "skip"]

.IP "Description:" 4
\f[I]Event(s)\f[]
for which IDs get written to an
\f[I]archive\f[].

Available events are:
\f[I]file\f[], \f[I]skip\f[]


.SS extractor.*.archive-format
.IP "Type:" 6
\f[I]string\f[]

.IP "Example:" 4
"{id}_{offset}"

.IP "Description:" 4
An alternative \f[I]format string\f[] to build archive IDs with.


.SS extractor.*.archive-mode
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"file"\f[]

.IP "Description:" 4
Controls when to write \f[I]archive IDs\f[]
to the archive database.

.br
* \f[I]"file"\f[]: Write IDs immediately
after completing or skipping a file download.
.br
* \f[I]"memory"\f[]: Keep IDs in memory
and only write them after successful job completion.


.SS extractor.*.archive-prefix
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
.br
* \f[I]""\f[] when \f[I]archive-table\f[] is set
.br
* \f[I]"{category}"\f[] otherwise

.IP "Description:" 4
Prefix for archive IDs.


.SS extractor.*.archive-pragma
.IP "Type:" 6
\f[I]list\f[] of \f[I]strings\f[]

.IP "Example:" 4
["journal_mode=WAL", "synchronous=NORMAL"]

.IP "Description:" 4
A list of SQLite \f[I]PRAGMA\f[] statements to run during archive initialization.

See \f[I]<https://www.sqlite.org/pragma.html#toc>\f[]
for available \f[I]PRAGMA\f[] statements and further details.


.SS extractor.*.archive-table
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"archive"\f[]

.IP "Example:" 4
"{category}"

.IP "Description:" 4
\f[I]Format string\f[] selecting the archive database table name.


.SS extractor.*.actions
.IP "Type:" 6
.br
* \f[I]object\f[] (pattern -> action(s))
.br
* \f[I]list\f[] of \f[I]lists\f[] with pattern -> action(s) pairs as elements

.IP "Example:" 4
.. code:: json

{
"info:Logging in as .+"   : "level = debug",
"warning:(?i)unable to .+": "exit 127",
"error"                   : [
"status \f[I]= 1",
"exec notify.sh 'gdl error'",
"abort"
]
}

.. code:: json

[
["info:Logging in as .+"   , "level = debug"],
["warning:(?i)unable to .+", "exit 127"     ],
["error"                   , [
"status \f[]= 1",
"exec notify.sh 'gdl error'",
"abort"
]]
]


.IP "Description:" 4
Perform an \f[I]action\f[] when logging a message matched by \f[I]pattern\f[].

\f[I]pattern\f[] is parsed as severity level (\f[I]debug\f[], \f[I]info\f[], \f[I]warning\f[], \f[I]error\f[], or integer value)
followed by an optional \f[I]Python Regular Expression\f[]
separated by a colon \f[I]:\f[].
Using \f[I]*\f[] as level or leaving it empty
matches logging messages of all levels
(e.g. \f[I]*:<re>\f[] or \f[I]:<re>\f[]).

\f[I]action\f[] is parsed as action type
followed by (optional) arguments.

It is possible to specify more than one \f[I]action\f[] per \f[I]pattern\f[]
by providing them as a \f[I]list\f[]: \f[I]["<action1>", "<action2>", …]\f[]

Supported Action Types:

\f[I]status\f[]:
Modify job exit status.
.br
Expected syntax is \f[I]<operator> <value>\f[] (e.g. \f[I]= 100\f[]).
.br

Supported operators are
\f[I]=\f[] (assignment),
\f[I]&\f[] (bitwise AND),
\f[I]|\f[] (bitwise OR),
\f[I]^\f[] (bitwise XOR).
\f[I]level\f[]:
Modify severity level of the current logging message.
.br
Can be one of \f[I]debug\f[], \f[I]info\f[], \f[I]warning\f[], \f[I]error\f[] or an integer value.
.br
\f[I]print\f[]:
Write argument to stdout.
\f[I]exec\f[]:
Run a shell command.
\f[I]abort\f[]:
Stop the current extractor run.
\f[I]terminate\f[]:
Stop the current extractor run, including parent extractors.
\f[I]restart\f[]:
Restart the current extractor run.
\f[I]wait\f[]:
Sleep for a given \f[I]Duration\f[] or
.br
wait until Enter is pressed when no argument was given.
.br
\f[I]exit\f[]:
Exit the program with the given argument as exit status.


.SS extractor.*.postprocessors
.IP "Type:" 6
.br
* \f[I]Postprocessor Configuration\f[] object
.br
* \f[I]list\f[] of \f[I]Postprocessor Configuration\f[] objects

.IP "Example:" 4
.. code:: json

[
{
"name": "zip" ,
"compression": "store"
},
{
"name": "exec",
"command": ["/home/<USER>/script", "{category}", "{image_id}"]
}
]


.IP "Description:" 4
A list of \f[I]post processors\f[]
to be applied to each downloaded file in the specified order.

Unlike other options, a \f[I]postprocessors\f[] setting at a deeper level
.br
does not override any \f[I]postprocessors\f[] setting at a lower level.
Instead, all post processors from all applicable \f[I]postprocessors\f[]
.br
settings get combined into a single list.

For example

.br
* an \f[I]mtime\f[] post processor at \f[I]extractor.postprocessors\f[],
.br
* a \f[I]zip\f[] post processor at \f[I]extractor.pixiv.postprocessors\f[],
.br
* and using \f[I]--exec\f[]

will run all three post processors - \f[I]mtime\f[], \f[I]zip\f[], \f[I]exec\f[] -
for each downloaded \f[I]pixiv\f[] file.


.SS extractor.*.postprocessor-options
.IP "Type:" 6
\f[I]object\f[] (name -> value)

.IP "Example:" 4
.. code:: json

{
"archive": null,
"keep-files": true
}


.IP "Description:" 4
Additional \f[I]Postprocessor Options\f[] that get added to each individual
\f[I]post processor object\f[]
before initializing it and evaluating filters.


.SS extractor.*.retries
.IP "Type:" 6
\f[I]integer\f[]

.IP "Default:" 9
\f[I]4\f[]

.IP "Description:" 4
Maximum number of times a failed HTTP request is retried before
giving up, or \f[I]-1\f[] for infinite retries.


.SS extractor.*.retry-codes
.IP "Type:" 6
\f[I]list\f[] of \f[I]integers\f[]

.IP "Example:" 4
[404, 429, 430]

.IP "Description:" 4
Additional \f[I]HTTP response status codes\f[]
to retry an HTTP request on.

\f[I]2xx\f[] codes (success responses) and
\f[I]3xx\f[] codes (redirection messages)
will never be retried and always count as success,
regardless of this option.

\f[I]5xx\f[] codes (server error responses)  will always be retried,
regardless of this option.


.SS extractor.*.timeout
.IP "Type:" 6
\f[I]float\f[]

.IP "Default:" 9
\f[I]30.0\f[]

.IP "Description:" 4
Amount of time (in seconds) to wait for a successful connection
and response from a remote server.

This value gets internally used as the \f[I]timeout\f[] parameter for the
\f[I]requests.request()\f[] method.


.SS extractor.*.verify
.IP "Type:" 6
.br
* \f[I]bool\f[]
.br
* \f[I]string\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Controls whether to verify SSL/TLS certificates for HTTPS requests.

If this is a \f[I]string\f[], it must be the path to a CA bundle to use
instead of the default certificates.

This value gets internally used as the \f[I]verify\f[] parameter for the
\f[I]requests.request()\f[] method.


.SS extractor.*.download
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Controls whether to download media files.

Setting this to \f[I]false\f[] won't download any files, but all other
functions (\f[I]postprocessors\f[], \f[I]download archive\f[], etc.)
will be executed as normal.


.SS extractor.*.fallback
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Use fallback download URLs when a download fails.


.SS extractor.*.image-range
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]list\f[] of \f[I]strings\f[]

.IP "Examples:" 4
.br
* \f[I]"10-20"\f[]
.br
* \f[I]"-5, 10, 30-50, 100-"\f[]
.br
* \f[I]"10:21, 30:51:2, :5, 100:"\f[]
.br
* \f[I]["-5", "10", "30-50", "100-"]\f[]

.IP "Description:" 4
Index range(s) selecting which files to download.

These can be specified as

.br
* index: \f[I]3\f[] (file number 3)
.br
* range: \f[I]2-4\f[] (files 2, 3, and 4)
.br
* \f[I]slice\f[]: \f[I]3:8:2\f[] (files 3, 5, and 7)

Arguments for range and slice notation are optional
.br
and will default to begin (\f[I]1\f[]) or end (\f[I]sys.maxsize\f[]) if omitted.
For example \f[I]5-\f[], \f[I]5:\f[], and \f[I]5::\f[] all mean "Start at file number 5".
.br

Note: The index of the first file is \f[I]1\f[].


.SS extractor.*.chapter-range
.IP "Type:" 6
\f[I]string\f[]

.IP "Description:" 4
Like \f[I]image-range\f[],
but applies to delegated URLs like manga chapters, etc.


.SS extractor.*.image-filter
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]list\f[] of \f[I]strings\f[]

.IP "Examples:" 4
.br
* \f[I]"re.search(r'foo(bar)+', description)"\f[]
.br
* \f[I]["width >= 1200", "width/height > 1.2"]\f[]

.IP "Description:" 4
Python expression controlling which files to download.

A file only gets downloaded when *all* of the given expressions evaluate to \f[I]True\f[].

Available values are the filename-specific ones listed by \f[I]-K\f[] or \f[I]-j\f[].


.SS extractor.*.chapter-filter
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]list\f[] of \f[I]strings\f[]

.IP "Examples:" 4
.br
* \f[I]"lang == 'en'"\f[]
.br
* \f[I]["language == 'French'", "10 <= chapter < 20"]\f[]

.IP "Description:" 4
Like \f[I]image-filter\f[],
but applies to delegated URLs like manga chapters, etc.


.SS extractor.*.image-unique
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Ignore image URLs that have been encountered before during the
current extractor run.


.SS extractor.*.chapter-unique
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Like \f[I]image-unique\f[],
but applies to delegated URLs like manga chapters, etc.


.SS extractor.*.date-format
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"%Y-%m-%dT%H:%M:%S"\f[]

.IP "Description:" 4
Format string used to parse \f[I]string\f[] values of
date-min and date-max.

See \f[I]strptime\f[] for a list of formatting directives.

Note: Despite its name, this option does **not** control how
\f[I]{date}\f[] metadata fields are formatted.
To use a different formatting for those values other than the default
\f[I]%Y-%m-%d %H:%M:%S\f[], put \f[I]strptime\f[] formatting directives
after a colon \f[I]:\f[], for example \f[I]{date:%Y%m%d}\f[].


.SS extractor.*.write-pages
.IP "Type:" 6
.br
* \f[I]bool\f[]
.br
* \f[I]string\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
During data extraction,
write received HTTP request data
to enumerated files in the current working directory.

Special values:

.br
* \f[I]"all"\f[]: Include HTTP request and response headers. Hide \f[I]Authorization\f[], \f[I]Cookie\f[], and \f[I]Set-Cookie\f[] values.
.br
* \f[I]"ALL"\f[]: Include all HTTP request and response headers.


.SH EXTRACTOR-SPECIFIC OPTIONS
.SS extractor.ao3.formats
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]list\f[] of \f[I]strings\f[]

.IP "Default:" 9
\f[I]"pdf"\f[]

.IP "Example:" 4
.br
* "azw3,epub,mobi,pdf,html"
.br
* ["azw3", "epub", "mobi", "pdf", "html"]

.IP "Description:" 4
Format(s) to download.


.SS extractor.arcalive.emoticons
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Download emoticon images.


.SS extractor.arcalive.gifs
.IP "Type:" 6
.br
* \f[I]bool\f[]
.br
* \f[I]string\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Try to download \f[I].gif\f[] versions of \f[I].mp4\f[] videos.

\f[I]true\f[] | \f[I]"fallback\f[]
Use the \f[I].gif\f[] version as primary URL
and provide the \f[I].mp4\f[] one as
\f[I]fallback\f[].
\f[I]"check"\f[]
Check whether a \f[I].gif\f[] version is available
by sending an extra HEAD request.
\f[I]false\f[]
Always download the \f[I].mp4\f[] version.


.SS extractor.artstation.external
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Try to follow external URLs of embedded players.


.SS extractor.artstation.max-posts
.IP "Type:" 6
\f[I]integer\f[]

.IP "Default:" 9
\f[I]null\f[]

.IP "Description:" 4
Limit the number of posts/projects to download.


.SS extractor.artstation.previews
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Download video previews.


.SS extractor.artstation.videos
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Download video clips.


.SS extractor.artstation.search.pro-first
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Enable the "Show Studio and Pro member artwork first" checkbox
when retrieving search results.


.SS extractor.aryion.recursive
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Controls the post extraction strategy.

.br
* \f[I]true\f[]: Start on users' main gallery pages and recursively
descend into subfolders
.br
* \f[I]false\f[]: Get posts from "Latest Updates" pages


.SS extractor.batoto.domain
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"auto"\f[]

.IP "Example:" 4
"mangatoto.org"

.IP "Description:" 4
Specifies the domain used by \f[I]batoto\f[] extractors.

\f[I]"auto"\f[] | \f[I]"url"\f[]
Use the input URL's domain
\f[I]"nolegacy"\f[]
Use the input URL's domain
.br
- replace legacy domains with \f[I]"xbato.org"\f[]
\f[I]"nowarn"\f[]
Use the input URL's domain
.br
- do not warn about legacy domains
any \f[I]string\f[]
Use this domain


.SS extractor.bbc.width
.IP "Type:" 6
\f[I]integer\f[]

.IP "Default:" 9
\f[I]1920\f[]

.IP "Description:" 4
Specifies the requested image width.

This value must be divisble by 16 and gets rounded down otherwise.
The maximum possible value appears to be \f[I]1920\f[].


.SS extractor.behance.modules
.IP "Type:" 6
\f[I]list\f[] of \f[I]strings\f[]

.IP "Default:" 9
\f[I]["image", "video", "mediacollection", "embed"]\f[]

.IP "Description:" 4
Selects which gallery modules to download from.

Supported module types are
\f[I]image\f[], \f[I]video\f[], \f[I]mediacollection\f[], \f[I]embed\f[], \f[I]text\f[].


.SS extractor.[blogger].api-key
.IP "Type:" 6
\f[I]string\f[]

.IP "Description:" 4
Custom Blogger API key.

https://developers.google.com/blogger/docs/3.0/using#APIKey


.SS extractor.[blogger].videos
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Download embedded videos hosted on https://www.blogger.com/


.SS extractor.bluesky.include
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]list\f[] of \f[I]strings\f[]

.IP "Default:" 9
.br
* \f[I]"posts"\f[] if
\f[I]reposts\f[] or
\f[I]quoted\f[] is enabled
.br
* \f[I]"media"\f[] otherwise

.IP "Example:" 4
.br
* "avatar,background,posts"
.br
* ["avatar", "background", "posts"]

.IP "Description:" 4
A (comma-separated) list of subcategories to include
when processing a user profile.

Possible values are
\f[I]"info"\f[],
\f[I]"avatar"\f[],
\f[I]"background"\f[],
\f[I]"posts"\f[],
\f[I]"replies"\f[],
\f[I]"media"\f[],
\f[I]"video"\f[],
\f[I]"likes"\f[],

It is possible to use \f[I]"all"\f[] instead of listing all values separately.


.SS extractor.bluesky.likes.endpoint
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"listRecords"\f[]

.IP "Description:" 4
API endpoint to use for retrieving liked posts.

\f[I]"listRecords"\f[]
Use the results from
.br
\f[I]com.atproto.repo.listRecords\f[]
Requires no login and alows accessing likes of all users,
.br
but uses one request to
\f[I]getPostThread\f[]
per post,
\f[I]"getActorLikes"\f[]
Use the results from
.br
\f[I]app.bsky.feed.getActorLikes\f[]
Requires login and only allows accessing your own likes.
.br


.SS extractor.bluesky.metadata
.IP "Type:" 6
.br
* \f[I]bool\f[]
.br
* \f[I]string\f[]
.br
* \f[I]list\f[] of \f[I]strings\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Example:" 4
.br
* "facets,user"
.br
* ["facets", "user"]

.IP "Description:" 4
Extract additional metadata.

.br
* \f[I]facets\f[]: \f[I]hashtags\f[], \f[I]mentions\f[], and \f[I]uris\f[]
.br
* \f[I]user\f[]: detailed \f[I]user\f[] metadata for the user referenced in the input URL
(See \f[I]app.bsky.actor.getProfile\f[]).


.SS extractor.bluesky.likes.depth
.IP "Type:" 6
\f[I]integer\f[]

.IP "Default:" 9
\f[I]0\f[]

.IP "Description:" 4
Sets the maximum depth of returned reply posts.

(See depth parameter of \f[I]app.bsky.feed.getPostThread\f[])


.SS extractor.bluesky.quoted
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Fetch media from quoted posts.


.SS extractor.bluesky.reposts
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Process reposts.


.SS extractor.bluesky.videos
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Download videos.


.SS extractor.boosty.allowed
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Request only available posts.


.SS extractor.boosty.bought
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Request only purchased posts for \f[I]feed\f[] results.


.SS extractor.boosty.metadata
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Provide detailed \f[I]user\f[] metadata.


.SS extractor.boosty.videos
.IP "Type:" 6
.br
* \f[I]bool\f[]
.br
* \f[I]list\f[] of \f[I]strings\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Example:" 4
["full_hd", "high", "medium"]

.IP "Description:" 4
Download videos.

If this is a \f[I]list\f[], it selects which format to try to download.
.br
Possibly available formats are
.br

.br
* \f[I]ultra_hd\f[] (2160p)
.br
* \f[I]quad_hd\f[] (1440p)
.br
* \f[I]full_hd\f[] (1080p)
.br
* \f[I]high\f[] (720p)
.br
* \f[I]medium\f[] (480p)
.br
* \f[I]low\f[] (360p)
.br
* \f[I]lowest\f[] (240p)
.br
* \f[I]tiny\f[] (144p)


.SS extractor.bunkr.endpoint
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"/api/_001"\f[]

.IP "Description:" 4
API endpoint for retrieving file URLs.


.SS extractor.bunkr.tlds
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Controls which \f[I]bunkr\f[] TLDs to accept.

.br
* \f[I]true\f[]: Match URLs with *all* possible TLDs (e.g. \f[I]bunkr.xyz\f[] or \f[I]bunkrrr.duck\f[])
.br
* \f[I]false\f[]: Match only URLs with known TLDs


.SS extractor.cien.files
.IP "Type:" 6
\f[I]list\f[] of \f[I]strings\f[]

.IP "Default:" 9
\f[I]["image", "video", "download", "gallery"]\f[]

.IP "Description:" 4
Determines the type and order of files to download.

Available types are
\f[I]image\f[],
\f[I]video\f[],
\f[I]download\f[],
\f[I]gallery\f[].


.SS extractor.civitai.api
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"trpc"\f[]

.IP "Description:" 4
Selects which API endpoints to use.

.br
* \f[I]"rest"\f[]: \f[I]Public REST API\f[]
.br
* \f[I]"trpc"\f[]: Internal tRPC API


.SS extractor.civitai.api-key
.IP "Type:" 6
\f[I]string\f[]

.IP "Description:" 4
The API Key value generated in your
\f[I]User Account Settings\f[]
to make authorized API requests.

See \f[I]API/Authorization\f[]
for details.


.SS extractor.civitai.files
.IP "Type:" 6
\f[I]list\f[] of \f[I]strings\f[]

.IP "Default:" 9
\f[I]["image"]\f[]

.IP "Description:" 4
Determines the type and order of files to download when processing models.

Available types are
\f[I]model\f[],
\f[I]image\f[],
\f[I]gallery\f[].


.SS extractor.civitai.include
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]list\f[] of \f[I]strings\f[]

.IP "Default:" 9
\f[I]["user-models", "user-posts"]\f[]

.IP "Description:" 4
A (comma-separated) list of subcategories to include
when processing a user profile.

Possible values are

.br
* \f[I]"user-models"\f[]
.br
* \f[I]"user-posts"\f[]
.br
* \f[I]"user-images"\f[]
.br
* \f[I]"user-videos"\f[]

It is possible to use \f[I]"all"\f[] instead of listing all values separately.


.SS extractor.civitai.metadata
.IP "Type:" 6
.br
* \f[I]bool\f[]
.br
* \f[I]string\f[]
.br
* \f[I]list\f[] of \f[I]strings\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Example:" 4
.br
* "generation,version"
.br
* ["generation", "version"]

.IP "Description:" 4
Extract additional \f[I]generation\f[] and \f[I]version\f[] metadata.

Note: This requires 1 additional HTTP request per image or video.


.SS extractor.civitai.nsfw
.IP "Type:" 6
.br
* \f[I]bool\f[]
.br
* \f[I]string\f[] (\f[I]"api": "rest"\f[])
.br
* \f[I]integer\f[] (\f[I]"api": "trpc"\f[])

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Download NSFW-rated images.

.br
* For \f[I]"api": "rest"\f[], this can be one of
\f[I]"None"\f[], \f[I]"Soft"\f[], \f[I]"Mature"\f[], \f[I]"X"\f[]
to set the highest returned mature content flag.

.br
* For \f[I]"api": "trpc"\f[], this can be an \f[I]integer\f[]
whose bits select the returned mature content flags.

For example, \f[I]28\f[] (\f[I]4\f[I]8\f[]16\f[])  would return only
\f[I]R\f[], \f[I]X\f[], and \f[I]XXX\f[] rated images,
while \f[I]3\f[] (\f[I]1|2\f[]) would return only
\f[I]None\f[] and \f[I]Soft\f[] rated images,


.SS extractor.civitai.quality
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]list\f[] of \f[I]strings\f[]

.IP "Default:" 9
\f[I]"original=true"\f[]

.IP "Example:" 4
.br
* "width=1280,quality=90"
.br
* ["width=1280", "quality=90"]

.IP "Description:" 4
A (comma-separated) list of image quality options
to pass with every image URL.

Known available options include \f[I]original\f[], \f[I]quality\f[], \f[I]width\f[]

Note: Set this option to an arbitrary letter, e.g., \f[I]"w"\f[],
to download images in JPEG format at their original resolution.


.SS extractor.civitai.quality-videos
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]list\f[] of \f[I]strings\f[]

.IP "Default:" 9
\f[I]"quality=100"\f[]

.IP "Example:" 4
.br
* "+transcode=true,quality=100"
.br
* ["+", "transcode=true", "quality=100"]

.IP "Description:" 4
A (comma-separated) list of video quality options
to pass with every video URL.

Known available options include \f[I]original\f[], \f[I]quality\f[], \f[I]transcode\f[]

Use \f[I]+\f[] as first character to add the given options to the
\f[I]quality\f[] ones.


.SS extractor.cyberdrop.domain
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]null\f[]

.IP "Example:" 4
"cyberdrop.to"

.IP "Description:" 4
Specifies the domain used by \f[I]cyberdrop\f[] regardless of input URL.

Setting this option to \f[I]"auto"\f[]
uses the same domain as a given input URL.


.SS extractor.[Danbooru].external
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
For unavailable or restricted posts,
follow the \f[I]source\f[] and download from there if possible.


.SS extractor.[Danbooru].favgroup.order-posts
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"pool"\f[]

.IP "Description:" 4
Controls the order in which \f[I]pool\f[]/\f[I]favgroup\f[] posts are returned.

\f[I]"pool"\f[] \f[I] \f[I]"pool_asc"\f[] \f[] \f[I]"asc"\f[] \f[I] \f[I]"asc_pool"\f[]
Pool order
\f[I]"pool_desc"\f[] \f[] \f[I]"desc_pool"\f[] \f[I] \f[I]"desc"\f[]
Reverse Pool order
\f[I]"id"\f[] \f[] \f[I]"id_desc"\f[] \f[I] \f[I]"desc_id"\f[]
Descending Post ID order
\f[I]"id_asc"\f[] \f[] \f[I]"asc_id"\f[]
Ascending Post ID order


.SS extractor.[Danbooru].ugoira
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Controls the download target for Ugoira posts.

.br
* \f[I]true\f[]: Original ZIP archives
.br
* \f[I]false\f[]: Converted video files


.SS extractor.[Danbooru].metadata
.IP "Type:" 6
.br
* \f[I]bool\f[]
.br
* \f[I]string\f[]
.br
* \f[I]list\f[] of \f[I]strings\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Example:" 4
.br
* "replacements,comments,ai_tags"
.br
* ["replacements", "comments", "ai_tags"]

.IP "Description:" 4
Extract additional metadata
(notes, artist commentary, parent, children, uploader)

It is possible to specify a custom list of metadata includes.
See \f[I]available_includes\f[]
for possible field names. \f[I]aibooru\f[] also supports \f[I]ai_metadata\f[].

Note: This requires 1 additional HTTP request per 200-post batch.


.SS extractor.[Danbooru].threshold
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]integer\f[]

.IP "Default:" 9
\f[I]"auto"\f[]

.IP "Description:" 4
Stop paginating over API results if the length of a batch of returned
posts is less than the specified number. Defaults to the per-page limit
of the current instance, which is 200.

Note: Changing this setting is normally not necessary. When the value is
greater than the per-page limit, gallery-dl will stop after the first
batch. The value cannot be less than 1.


.SS extractor.deviantart.auto-watch
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Automatically watch users when encountering "Watchers-Only Deviations"
(requires a \f[I]refresh-token\f[]).


.SS extractor.deviantart.auto-unwatch
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
After watching a user through \f[I]auto-watch\f[],
unwatch that user at the end of the current extractor run.


.SS extractor.deviantart.comments
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Extract \f[I]comments\f[] metadata.


.SS extractor.deviantart.comments-avatars
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Download the avatar of each commenting user.

Note: Enabling this option also enables deviantart.comments_.


.SS extractor.deviantart.extra
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Download extra Sta.sh resources from
description texts and journals.

Note: Enabling this option also enables deviantart.metadata_.


.SS extractor.deviantart.flat
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Select the directory structure created by the Gallery- and
Favorite-Extractors.

.br
* \f[I]true\f[]: Use a flat directory structure.
.br
* \f[I]false\f[]: Collect a list of all gallery-folders or
favorites-collections and transfer any further work to other
extractors (\f[I]folder\f[] or \f[I]collection\f[]), which will then
create individual subdirectories for each of them.

Note: Going through all gallery folders will not be able to
fetch deviations which aren't in any folder.


.SS extractor.deviantart.folders
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Provide a \f[I]folders\f[] metadata field that contains the names of all
folders a deviation is present in.

Note: Gathering this information requires a lot of API calls.
Use with caution.


.SS extractor.deviantart.group
.IP "Type:" 6
.br
* \f[I]bool\f[]
.br
* \f[I]string\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Check whether the profile name in a given URL
belongs to a group or a regular user.

When disabled, assume every given profile name
belongs to a regular user.

Special values:

.br
* \f[I]"skip"\f[]: Skip groups


.SS extractor.deviantart.include
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]list\f[] of \f[I]strings\f[]

.IP "Default:" 9
\f[I]"gallery"\f[]

.IP "Example:" 4
.br
* "favorite,journal,scraps"
.br
* ["favorite", "journal", "scraps"]

.IP "Description:" 4
A (comma-separated) list of subcategories to include
when processing a user profile.

Possible values are
\f[I]"avatar"\f[],
\f[I]"background"\f[],
\f[I]"gallery"\f[],
\f[I]"scraps"\f[],
\f[I]"journal"\f[],
\f[I]"favorite"\f[],
\f[I]"status"\f[].

It is possible to use \f[I]"all"\f[] instead of listing all values separately.


.SS extractor.deviantart.intermediary
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
For older non-downloadable images,
download a higher-quality \f[I]/intermediary/\f[] version.


.SS extractor.deviantart.journals
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"html"\f[]

.IP "Description:" 4
Selects the output format for textual content. This includes journals,
literature and status updates.

.br
* \f[I]"html"\f[]: HTML with (roughly) the same layout as on DeviantArt.
.br
* \f[I]"text"\f[]: Plain text with image references and HTML tags removed.
.br
* \f[I]"none"\f[]: Don't download textual content.


.SS extractor.deviantart.jwt
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Update \f[I]JSON Web Tokens\f[] (the \f[I]token\f[] URL parameter)
of otherwise non-downloadable, low-resolution images
to be able to download them in full resolution.

Note: No longer functional as of 2023-10-11


.SS extractor.deviantart.mature
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Enable mature content.

This option simply sets the \f[I]mature_content\f[] parameter for API
calls to either \f[I]"true"\f[] or \f[I]"false"\f[] and does not do any other
form of content filtering.


.SS extractor.deviantart.metadata
.IP "Type:" 6
.br
* \f[I]bool\f[]
.br
* \f[I]string\f[]
.br
* \f[I]list\f[] of \f[I]strings\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Example:" 4
.br
* "stats,submission"
.br
* ["camera", "stats", "submission"]

.IP "Description:" 4
Extract additional metadata for deviation objects.

Provides
\f[I]description\f[], \f[I]tags\f[], \f[I]license\f[], and \f[I]is_watching\f[]
fields when enabled.

It is possible to request extended metadata by specifying a list of

.br
* \f[I]camera\f[]     : EXIF information (if available)
.br
* \f[I]stats\f[]      : deviation statistics
.br
* \f[I]submission\f[] : submission information
.br
* \f[I]collection\f[] : favourited folder information (requires a \f[I]refresh token\f[])
.br
* \f[I]gallery\f[]    : gallery folder information (requires a \f[I]refresh token\f[])

Set this option to \f[I]"all"\f[] to request all extended metadata categories.

See \f[I]/deviation/metadata\f[]
for official documentation.


.SS extractor.deviantart.original
.IP "Type:" 6
.br
* \f[I]bool\f[]
.br
* \f[I]string\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Download original files if available.

Setting this option to \f[I]"images"\f[] only downloads original
files if they are images and falls back to preview versions for
everything else (archives, videos, etc.).


.SS extractor.deviantart.pagination
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"api"\f[]

.IP "Description:" 4
Controls when to stop paginating over API results.

.br
* \f[I]"api"\f[]: Trust the API and stop when \f[I]has_more\f[] is \f[I]false\f[].
.br
* \f[I]"manual"\f[]: Disregard \f[I]has_more\f[] and only stop when a batch of results is empty.


.SS extractor.deviantart.previews
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
For non-image files (archives, videos, etc.),
also download the file's preview image.

Set this option to \f[I]"all"\f[] to download previews for all files.


.SS extractor.deviantart.public
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Use a public access token for API requests.

Disable this option to *force* using a private token for all requests
when a \f[I]refresh token\f[] is provided.


.SS extractor.deviantart.quality
.IP "Type:" 6
.br
* \f[I]integer\f[]
.br
* \f[I]string\f[]

.IP "Default:" 9
\f[I]100\f[]

.IP "Description:" 4
JPEG quality level of images for which
an original file download is not available.

Set this to \f[I]"png"\f[] to download a PNG version of these images instead.


.SS extractor.deviantart.refresh-token
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]null\f[]

.IP "Description:" 4
The \f[I]refresh-token\f[] value you get from
\f[I]linking your DeviantArt account to gallery-dl\f[].

Using a \f[I]refresh-token\f[] allows you to access private or otherwise
not publicly available deviations.

Note: The \f[I]refresh-token\f[] becomes invalid
\f[I]after 3 months\f[]
or whenever your \f[I]cache file\f[] is deleted or cleared.


.SS extractor.deviantart.wait-min
.IP "Type:" 6
\f[I]integer\f[]

.IP "Default:" 9
\f[I]0\f[]

.IP "Description:" 4
Minimum wait time in seconds before API requests.


.SS extractor.deviantart.avatar.formats
.IP "Type:" 6
\f[I]list\f[] of \f[I]strings\f[]

.IP "Example:" 4
["original.jpg", "big.jpg", "big.gif", ".png"]

.IP "Description:" 4
Avatar URL formats to return.

Each format is parsed as \f[I]SIZE.EXT\f[].
.br
Leave \f[I]SIZE\f[] empty to download the regular, small avatar format.
.br


.SS extractor.deviantart.folder.subfolders
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Also extract subfolder content.


.SS extractor.discord.embeds
.IP "Type:" 6
\f[I]list\f[] of \f[I]strings\f[]

.IP "Default:" 9
\f[I]["image", "gifv", "video"]\f[]

.IP "Description:" 4
Selects which embed types to download from.

Supported embed types are
\f[I]image\f[], \f[I]gifv\f[], \f[I]video\f[], \f[I]rich\f[], \f[I]article\f[], \f[I]link\f[].


.SS extractor.discord.threads
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Extract threads from Discord text channels.


.SS extractor.discord.token
.IP "Type:" 6
\f[I]string\f[]

.IP "Description:" 4
Discord Bot Token for API requests.

You can follow \f[I]this guide\f[] to get a token.


.SS extractor.[E621].metadata
.IP "Type:" 6
.br
* \f[I]bool\f[]
.br
* \f[I]string\f[]
.br
* \f[I]list\f[] of \f[I]strings\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Example:" 4
.br
* "notes,pools"
.br
* ["notes", "pools"]

.IP "Description:" 4
Extract additional metadata (notes, pool metadata) if available.

Note: This requires 0-2 additional HTTP requests per post.


.SS extractor.[E621].threshold
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]integer\f[]

.IP "Default:" 9
\f[I]"auto"\f[]

.IP "Description:" 4
Stop paginating over API results if the length of a batch of returned
posts is less than the specified number. Defaults to the per-page limit
of the current instance, which is 320.

Note: Changing this setting is normally not necessary. When the value is
greater than the per-page limit, gallery-dl will stop after the first
batch. The value cannot be less than 1.


.SS extractor.exhentai.domain
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"auto"\f[]

.IP "Description:" 4
.br
* \f[I]"auto"\f[]: Use \f[I]e-hentai.org\f[] or \f[I]exhentai.org\f[]
depending on the input URL
.br
* \f[I]"e-hentai.org"\f[]: Use \f[I]e-hentai.org\f[] for all URLs
.br
* \f[I]"exhentai.org"\f[]: Use \f[I]exhentai.org\f[] for all URLs


.SS extractor.exhentai.fallback-retries
.IP "Type:" 6
\f[I]integer\f[]

.IP "Default:" 9
\f[I]2\f[]

.IP "Description:" 4
Number of times a failed image gets retried
or \f[I]-1\f[] for infinite retries.


.SS extractor.exhentai.fav
.IP "Type:" 6
\f[I]string\f[]

.IP "Example:" 4
"4"

.IP "Description:" 4
After downloading a gallery,
add it to your account's favorites as the given category number.

Note: Set this to "favdel" to remove galleries from your favorites.

Note: This will remove any Favorite Notes when applied
to already favorited galleries.


.SS extractor.exhentai.gp
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"resized"\f[]

.IP "Description:" 4
Selects how to handle "you do not have enough GP" errors.

.br
* "resized": Continue downloading \f[I]non-original\f[] images.
.br
* "stop": Stop the current extractor run.
.br
* "wait": Wait for user input before retrying the current image.


.SS extractor.exhentai.limits
.IP "Type:" 6
\f[I]integer\f[]

.IP "Default:" 9
\f[I]null\f[]

.IP "Description:" 4
Sets a custom image download limit and
stops extraction when it gets exceeded.


.SS extractor.exhentai.metadata
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Load extended gallery metadata from the
\f[I]API\f[].

Adds \f[I]archiver_key\f[], \f[I]posted\f[], and \f[I]torrents\f[].
Makes \f[I]date\f[] and \f[I]filesize\f[] more precise.


.SS extractor.exhentai.original
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Download full-sized original images if available.


.SS extractor.exhentai.source
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"gallery"\f[]

.IP "Description:" 4
Selects an alternative source to download files from.

.br
* \f[I]"hitomi"\f[]:  Download the corresponding gallery from \f[I]hitomi.la\f[]


.SS extractor.exhentai.tags
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Group \f[I]tags\f[] by type and
provide them as \f[I]tags_<type>\f[] metadata fields,
for example \f[I]tags_artist\f[] or \f[I]tags_character\f[].


.SS extractor.facebook.author-followups
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "description:" 4
Extract comments that include photo attachments made by the author of the post.


.SS extractor.facebook.videos
.IP "Type:" 6
.br
* \f[I]bool\f[]
.br
* \f[I]string\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Control video download behavior.

.br
* \f[I]true\f[]: Extract and download video & audio separately.
.br
* \f[I]"ytdl"\f[]: Let \f[I]ytdl\f[] handle video extraction and download, and merge video & audio streams.
.br
* \f[I]false\f[]: Ignore videos.


.SS extractor.fanbox.comments
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Extract \f[I]comments\f[] metadata.

Note: This requires 1 or more additional API requests per post,
depending on the number of comments.


.SS extractor.fanbox.embeds
.IP "Type:" 6
.br
* \f[I]bool\f[]
.br
* \f[I]string\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Control behavior on embedded content from external sites.

.br
* \f[I]true\f[]: Extract embed URLs and download them if supported
(videos are not downloaded).
.br
* \f[I]"ytdl"\f[]: Like \f[I]true\f[], but let \f[I]ytdl\f[] handle video
extraction and download for YouTube, Vimeo, and SoundCloud embeds.
.br
* \f[I]false\f[]: Ignore embeds.


.SS extractor.fanbox.metadata
.IP "Type:" 6
.br
* \f[I]bool\f[]
.br
* \f[I]string\f[]
.br
* \f[I]list\f[] of \f[I]strings\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Example:" 4
.br
* user,plan,comments
.br
* ["user", "plan", "comments"]

.IP "Description:" 4
Extract \f[I]plan\f[] and extended \f[I]user\f[] metadata.

Supported fields when selecting which data to extract are

.br
* \f[I]comments\f[]
.br
* \f[I]plan\f[]
.br
* \f[I]user\f[]

Note: \f[I]comments\f[] can also be enabled via
\f[I]fanbox.comments\f[]


.SS extractor.flickr.access-token & .access-token-secret
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]null\f[]

.IP "Description:" 4
The \f[I]access_token\f[] and \f[I]access_token_secret\f[] values you get
from \f[I]linking your Flickr account to gallery-dl\f[].


.SS extractor.flickr.contexts
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
For each photo, return the albums and pools it belongs to
as \f[I]set\f[] and \f[I]pool\f[] metadata.

Note: This requires 1 additional API call per photo.
See \f[I]flickr.photos.getAllContexts\f[] for details.


.SS extractor.flickr.exif
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
For each photo, return its EXIF/TIFF/GPS tags
as \f[I]exif\f[] and \f[I]camera\f[] metadata.

Note: This requires 1 additional API call per photo.
See \f[I]flickr.photos.getExif\f[] for details.


.SS extractor.flickr.info
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
For each photo, retrieve its "full" metadata as provided by
\f[I]flickr.photos.getInfo\f[]

Note: This requires 1 additional API call per photo.


.SS extractor.flickr.metadata
.IP "Type:" 6
.br
* \f[I]bool\f[]
.br
* \f[I]string\f[]
.br
* \f[I]list\f[] of \f[I]strings\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Example:" 4
.br
* license,last_update,machine_tags
.br
* ["license", "last_update", "machine_tags"]

.IP "Description:" 4
Extract additional metadata
(license, date_taken, original_format, last_update, geo, machine_tags, o_dims)

It is possible to specify a custom list of metadata includes.
See \f[I]the extras parameter\f[]
in \f[I]Flickr's API docs\f[]
for possible field names.


.SS extractor.flickr.profile
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Extract additional \f[I]user\f[] profile metadata.

Note: This requires 1 additional API call per user profile.
See \f[I]flickr.people.getInfo\f[] for details.


.SS extractor.flickr.videos
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Extract and download videos.


.SS extractor.flickr.size-max
.IP "Type:" 6
.br
* \f[I]integer\f[]
.br
* \f[I]string\f[]

.IP "Default:" 9
\f[I]null\f[]

.IP "Description:" 4
Sets the maximum allowed size for downloaded images.

.br
* If this is an \f[I]integer\f[], it specifies the maximum image dimension
(width and height) in pixels.
.br
* If this is a \f[I]string\f[], it should be one of Flickr's format specifiers
(\f[I]"Original"\f[], \f[I]"Large"\f[], ... or \f[I]"o"\f[], \f[I]"k"\f[], \f[I]"h"\f[],
\f[I]"l"\f[], ...) to use as an upper limit.


.SS extractor.furaffinity.descriptions
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"text"\f[]

.IP "Description:" 4
Controls the format of \f[I]description\f[] metadata fields.

.br
* \f[I]"text"\f[]: Plain text with HTML tags removed
.br
* \f[I]"html"\f[]: Raw HTML content


.SS extractor.furaffinity.external
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Follow external URLs linked in descriptions.


.SS extractor.furaffinity.include
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]list\f[] of \f[I]strings\f[]

.IP "Default:" 9
\f[I]"gallery"\f[]

.IP "Example:" 4
.br
* "scraps,favorite"
.br
* ["scraps", "favorite"]

.IP "Description:" 4
A (comma-separated) list of subcategories to include
when processing a user profile.

Possible values are
\f[I]"gallery"\f[], \f[I]"scraps"\f[], \f[I]"favorite"\f[].

It is possible to use \f[I]"all"\f[] instead of listing all values separately.


.SS extractor.furaffinity.layout
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"auto"\f[]

.IP "Description:" 4
Selects which site layout to expect when parsing posts.

.br
* \f[I]"auto"\f[]: Automatically differentiate between \f[I]"old"\f[] and \f[I]"new"\f[]
.br
* \f[I]"old"\f[]: Expect the *old* site layout
.br
* \f[I]"new"\f[]: Expect the *new* site layout


.SS extractor.gelbooru.api-key & .user-id
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]null\f[]

.IP "Description:" 4
Values from the API Access Credentials section found at the bottom of your
\f[I]Account Options\f[]
page.


.SS extractor.gelbooru.favorite.order-posts
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"desc"\f[]

.IP "Description:" 4
Controls the order in which favorited posts are returned.

.br
* \f[I]"asc"\f[]: Ascending favorite date order (oldest first)
.br
* \f[I]"desc"\f[]: Descending favorite date order (newest first)
.br
* \f[I]"reverse"\f[]: Same as \f[I]"asc"\f[]


.SS extractor.generic.enabled
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Match **all** URLs not otherwise supported by gallery-dl,
even ones without a \f[I]generic:\f[] prefix.


.SS extractor.gofile.api-token
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]null\f[]

.IP "Description:" 4
API token value found at the bottom of your \f[I]profile page\f[].

If not set, a temporary guest token will be used.


.SS extractor.gofile.website-token
.IP "Type:" 6
\f[I]string\f[]

.IP "Description:" 4
API token value used during API requests.

An invalid or not up-to-date value
will result in \f[I]401 Unauthorized\f[] errors.

Keeping this option unset will use an extra HTTP request
to attempt to fetch the current value used by gofile.


.SS extractor.gofile.recursive
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Recursively download files from subfolders.


.SS extractor.hentaifoundry.include
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]list\f[] of \f[I]strings\f[]

.IP "Default:" 9
\f[I]"pictures"\f[]

.IP "Example:" 4
.br
* "scraps,stories"
.br
* ["scraps", "stories"]

.IP "Description:" 4
A (comma-separated) list of subcategories to include
when processing a user profile.

Possible values are
\f[I]"pictures"\f[], \f[I]"scraps"\f[], \f[I]"stories"\f[], \f[I]"favorite"\f[].

It is possible to use \f[I]"all"\f[] instead of listing all values separately.


.SS extractor.hitomi.format
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"webp"\f[]

.IP "Description:" 4
Selects which image format to download.

Available formats are \f[I]"webp"\f[] and \f[I]"avif"\f[].


.SS extractor.imagechest.access-token
.IP "Type:" 6
\f[I]string\f[]

.IP "Description:" 4
Your personal Image Chest access token.

These tokens allow using the API instead of having to scrape HTML pages,
providing more detailed metadata.
(\f[I]date\f[], \f[I]description\f[], etc)

See https://imgchest.com/docs/api/1.0/general/authorization
for instructions on how to generate such a token.


.SS extractor.imgur.client-id
.IP "Type:" 6
\f[I]string\f[]

.IP "Description:" 4
Custom Client ID value for API requests.


.SS extractor.imgur.mp4
.IP "Type:" 6
.br
* \f[I]bool\f[]
.br
* \f[I]string\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Controls whether to choose the GIF or MP4 version of an animation.

.br
* \f[I]true\f[]: Follow Imgur's advice and choose MP4 if the
\f[I]prefer_video\f[] flag in an image's metadata is set.
.br
* \f[I]false\f[]: Always choose GIF.
.br
* \f[I]"always"\f[]: Always choose MP4.


.SS extractor.inkbunny.orderby
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"create_datetime"\f[]

.IP "Description:" 4
Value of the \f[I]orderby\f[] parameter for submission searches.

(See \f[I]API#Search\f[]
for details)


.SS extractor.instagram.api
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"rest"\f[]

.IP "Description:" 4
Selects which API endpoints to use.

.br
* \f[I]"rest"\f[]: REST API - higher-resolution media
.br
* \f[I]"graphql"\f[]: GraphQL API - lower-resolution media


.SS extractor.instagram.cursor
.IP "Type:" 6
.br
* \f[I]bool\f[]
.br
* \f[I]string\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Example:" 4
"3414259811154179155_25025320"

.IP "Description:" 4
Controls from which position to start the extraction process from.

.br
* \f[I]true\f[]: Start from the beginning.
Log the most recent \f[I]cursor\f[] value when interrupted before reaching the end.
.br
* \f[I]false\f[]: Start from the beginning.
.br
* any \f[I]string\f[]: Start from the position defined by this value.


.SS extractor.instagram.include
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]list\f[] of \f[I]strings\f[]

.IP "Default:" 9
\f[I]"posts"\f[]

.IP "Example:" 4
.br
* "stories,highlights,posts"
.br
* ["stories", "highlights", "posts"]

.IP "Description:" 4
A (comma-separated) list of subcategories to include
when processing a user profile.

Possible values are
\f[I]"posts"\f[],
\f[I]"reels"\f[],
\f[I]"tagged"\f[],
\f[I]"stories"\f[],
\f[I]"highlights"\f[],
\f[I]"info"\f[],
\f[I]"avatar"\f[].

It is possible to use \f[I]"all"\f[] instead of listing all values separately.


.SS extractor.instagram.max-posts
.IP "Type:" 6
\f[I]integer\f[]

.IP "Default:" 9
\f[I]null\f[]

.IP "Description:" 4
Limit the number of posts to download.


.SS extractor.instagram.metadata
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Provide extended \f[I]user\f[] metadata even when referring to a user by ID,
e.g. \f[I]instagram.com/id:12345678\f[].

Note: This metadata is always available when referring to a user by name,
e.g. \f[I]instagram.com/USERNAME\f[].


.SS extractor.instagram.order-files
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"asc"\f[]

.IP "Description:" 4
Controls the order in which files of each post are returned.

.br
* \f[I]"asc"\f[]: Same order as displayed in a post
.br
* \f[I]"desc"\f[]: Reverse order as displayed in a post
.br
* \f[I]"reverse"\f[]: Same as \f[I]"desc"\f[]

Note: This option does *not* affect \f[I]{num}\f[].
To enumerate files in reverse order, use \f[I]count - num + 1\f[].


.SS extractor.instagram.order-posts
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"asc"\f[]

.IP "Description:" 4
Controls the order in which posts are returned.

.br
* \f[I]"asc"\f[]: Same order as displayed
.br
* \f[I]"desc"\f[]: Reverse order as displayed
.br
* \f[I]"id"\f[] or \f[I]"id_asc"\f[]: Ascending order by ID
.br
* \f[I]"id_desc"\f[]: Descending order by ID
.br
* \f[I]"reverse"\f[]: Same as \f[I]"desc"\f[]

Note: This option only affects \f[I]highlights\f[].


.SS extractor.instagram.previews
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Download video previews.


.SS extractor.instagram.videos
.IP "Type:" 6
.br
* \f[I]bool\f[]
.br
* \f[I]string\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Controls video download behavior.

\f[I]true\f[] \f[I] \f[I]"dash"\f[] \f[] \f[I]"ytdl"\f[]
Download videos from \f[I]video_dash_manifest\f[] data using \f[I]ytdl\f[]
\f[I]"merged"\f[]
Download pre-merged video formats
\f[I]false\f[]
Do not download videos


.SS extractor.instagram.stories.split
.IP "Type:" 6
.br
* \f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Split \f[I]stories\f[] elements into separate posts.


.SS extractor.itaku.videos
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Download video files.


.SS extractor.kemonoparty.archives
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Extract additional metadata for \f[I]archives\f[] files, including
\f[I]file\f[], \f[I]file_list\f[], and \f[I]password\f[].

Note: This requires 1 additional HTTP request per \f[I]archives\f[] file.


.SS extractor.kemonoparty.comments
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Extract \f[I]comments\f[] metadata.

Note: This requires 1 additional HTTP request per post.


.SS extractor.kemonoparty.duplicates
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Controls how to handle duplicate files in a post.

.br
* \f[I]true\f[]: Download duplicates
.br
* \f[I]false\f[]: Ignore duplicates


.SS extractor.kemonoparty.dms
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Extract a user's direct messages as \f[I]dms\f[] metadata.


.SS extractor.kemonoparty.announcements
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Extract a user's announcements as \f[I]announcements\f[] metadata.


.SS extractor.kemonoparty.endpoint
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"posts"\f[]

.IP "Description:" 4
API endpoint to use for retrieving creator posts.

\f[I]"legacy"\f[]
Use the results from
.br
\f[I]/v1/{service}/user/{creator_id}/posts-legacy\f[]
Provides less metadata, but is more reliable at returning all posts.
.br
Supports filtering results by \f[I]tag\f[] query parameter.
.br
\f[I]"legacy+"\f[]
Use the results from
.br
\f[I]/v1/{service}/user/{creator_id}/posts-legacy\f[]
to retrieve post IDs
and one request to
.br
\f[I]/v1/{service}/user/{creator_id}/post/{post_id}\f[]
to get a full set of metadata for each.
\f[I]"posts"\f[]
Use the results from
.br
\f[I]/v1/{service}/user/{creator_id}\f[]
Provides more metadata, but might not return a creator's first/last posts.
.br


.SS extractor.kemonoparty.favorites
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"artist"\f[]

.IP "Description:" 4
Determines the type of favorites to be downloaded.

Available types are \f[I]artist\f[], and \f[I]post\f[].


.SS extractor.kemonoparty.files
.IP "Type:" 6
\f[I]list\f[] of \f[I]strings\f[]

.IP "Default:" 9
\f[I]["attachments", "file", "inline"]\f[]

.IP "Description:" 4
Determines the type and order of files to be downloaded.

Available types are \f[I]file\f[], \f[I]attachments\f[], and \f[I]inline\f[].


.SS extractor.kemonoparty.max-posts
.IP "Type:" 6
\f[I]integer\f[]

.IP "Default:" 9
\f[I]null\f[]

.IP "Description:" 4
Limit the number of posts to download.


.SS extractor.kemonoparty.metadata
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Extract \f[I]username\f[] and \f[I]user_profile\f[] metadata.


.SS extractor.kemonoparty.revisions
.IP "Type:" 6
.br
* \f[I]bool\f[]
.br
* \f[I]string\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Extract post revisions.

Set this to \f[I]"unique"\f[] to filter out duplicate revisions.

Note: This requires 1 additional HTTP request per post.


.SS extractor.kemonoparty.order-revisions
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"desc"\f[]

.IP "Description:" 4
Controls the order in which
\f[I]revisions\f[]
are returned.

.br
* \f[I]"asc"\f[]: Ascending order (oldest first)
.br
* \f[I]"desc"\f[]: Descending order (newest first)
.br
* \f[I]"reverse"\f[]: Same as \f[I]"asc"\f[]


.SS extractor.khinsider.covers
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Download album cover images.


.SS extractor.khinsider.format
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"mp3"\f[]

.IP "Description:" 4
The name of the preferred file format to download.

Use \f[I]"all"\f[] to download all available formats,
or a (comma-separated) list to select multiple formats.

If the selected format is not available,
the first in the list gets chosen (usually mp3).


.SS extractor.koharu.cbz
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Download each gallery as a single \f[I].cbz\f[] file.

Disabling this option causes a gallery
to be downloaded as individual image files.


.SS extractor.koharu.format
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]list\f[] of \f[I]strings\f[]

.IP "Default:" 9
\f[I]["0", "1600", "1280", "980", "780"]\f[]

.IP "Description:" 4
Name(s) of the image format to download.

When more than one format is given, the first available one is selected.

Possible formats are
.br
\f[I]"780"\f[], \f[I]"980"\f[], \f[I]"1280"\f[], \f[I]"1600"\f[], \f[I]"0"\f[] (original)
.br


.SS extractor.koharu.tags
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Group \f[I]tags\f[] by type and
provide them as \f[I]tags_<type>\f[] metadata fields,
for example \f[I]tags_artist\f[] or \f[I]tags_character\f[].


.SS extractor.lolisafe.domain
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]null\f[]

.IP "Description:" 4
Specifies the domain used by a \f[I]lolisafe\f[] extractor
regardless of input URL.

Setting this option to \f[I]"auto"\f[]
uses the same domain as a given input URL.


.SS extractor.luscious.gif
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Format in which to download animated images.

Use \f[I]true\f[] to download animated images as gifs and \f[I]false\f[]
to download as mp4 videos.


.SS extractor.mangadex.api-server
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"https://api.mangadex.org"\f[]

.IP "Description:" 4
The server to use for API requests.


.SS extractor.mangadex.api-parameters
.IP "Type:" 6
\f[I]object\f[] (name -> value)

.IP "Example:" 4
{"order[updatedAt]": "desc"}

.IP "Description:" 4
Additional query parameters to send when fetching manga chapters.

(See \f[I]/manga/{id}/feed\f[]
and \f[I]/user/follows/manga/feed\f[])


.SS extractor.mangadex.lang
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]list\f[] of \f[I]strings\f[]

.IP "Example:" 4
.br
* "en"
.br
* "fr,it"
.br
* ["fr", "it"]

.IP "Description:" 4
\f[I]ISO 639-1\f[] language codes
to filter chapters by.


.SS extractor.mangadex.ratings
.IP "Type:" 6
\f[I]list\f[] of \f[I]strings\f[]

.IP "Default:" 9
\f[I]["safe", "suggestive", "erotica", "pornographic"]\f[]

.IP "Description:" 4
List of acceptable content ratings for returned chapters.


.SS extractor.mangapark.source
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]integer\f[]

.IP "Example:" 4
.br
* "koala:en"
.br
* 15150116

.IP "Description:" 4
Select chapter source and language for a manga.

The general syntax is \f[I]"<source name>:<ISO 639-1 language code>"\f[].
.br
Both are optional, meaning \f[I]"koala"\f[], \f[I]"koala:"\f[], \f[I]":en"\f[],
.br
or even just \f[I]":"\f[] are possible as well.

Specifying the numeric \f[I]ID\f[] of a source is also supported.


.SS extractor.[mastodon].access-token
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]null\f[]

.IP "Description:" 4
The \f[I]access-token\f[] value you get from \f[I]linking your account to
gallery-dl\f[].

Note: gallery-dl comes with built-in tokens for \f[I]mastodon.social\f[],
\f[I]pawoo\f[] and \f[I]baraag\f[]. For other instances, you need to obtain an
\f[I]access-token\f[] in order to use usernames in place of numerical
user IDs.


.SS extractor.[mastodon].cards
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Fetch media from cards.


.SS extractor.[mastodon].reblogs
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Fetch media from reblogged posts.


.SS extractor.[mastodon].replies
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Fetch media from replies to other posts.


.SS extractor.[mastodon].text-posts
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Also emit metadata for text-only posts without media content.


.SS extractor.[misskey].access-token
.IP "Type:" 6
\f[I]string\f[]

.IP "Description:" 4
Your access token, necessary to fetch favorited notes.


.SS extractor.[misskey].renotes
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Fetch media from renoted notes.


.SS extractor.[misskey].replies
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Fetch media from replies to other notes.


.SS extractor.[moebooru].pool.metadata
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Extract extended \f[I]pool\f[] metadata.

Note: Not supported by all \f[I]moebooru\f[] instances.


.SS extractor.naver.videos
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Download videos.


.SS extractor.newgrounds.flash
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Download original Adobe Flash animations instead of pre-rendered videos.


.SS extractor.newgrounds.format
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]list\f[] of \f[I]string\f[]

.IP "Default:" 9
\f[I]"original"\f[]

.IP "Example:" 4
.br
* "720p"
.br
* ["mp4", "mov", "1080p", "720p"]

.IP "Description:" 4
Selects the preferred format for video downloads.

If the selected format is not available,
the next smaller one gets chosen.

If this is a \f[I]list\f[], try each given
filename extension in original resolution or recoded format
until an available format is found.


.SS extractor.newgrounds.include
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]list\f[] of \f[I]strings\f[]

.IP "Default:" 9
\f[I]"art"\f[]

.IP "Example:" 4
.br
* "movies,audio"
.br
* ["movies", "audio"]

.IP "Description:" 4
A (comma-separated) list of subcategories to include
when processing a user profile.

Possible values are
\f[I]"art"\f[], \f[I]"audio"\f[], \f[I]"games"\f[], \f[I]"movies"\f[].

It is possible to use \f[I]"all"\f[] instead of listing all values separately.


.SS extractor.nijie.include
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]list\f[] of \f[I]strings\f[]

.IP "Default:" 9
\f[I]"illustration,doujin"\f[]

.IP "Description:" 4
A (comma-separated) list of subcategories to include
when processing a user profile.

Possible values are
\f[I]"illustration"\f[], \f[I]"doujin"\f[], \f[I]"favorite"\f[], \f[I]"nuita"\f[].

It is possible to use \f[I]"all"\f[] instead of listing all values separately.


.SS extractor.nitter.quoted
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Fetch media from quoted Tweets.


.SS extractor.nitter.retweets
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Fetch media from Retweets.


.SS extractor.nitter.videos
.IP "Type:" 6
.br
* \f[I]bool\f[]
.br
* \f[I]string\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Control video download behavior.

.br
* \f[I]true\f[]: Download videos
.br
* \f[I]"ytdl"\f[]: Download videos using \f[I]ytdl\f[]
.br
* \f[I]false\f[]: Skip video Tweets


.SS extractor.oauth.browser
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Controls how a user is directed to an OAuth authorization page.

.br
* \f[I]true\f[]: Use Python's \f[I]webbrowser.open()\f[] method to automatically
open the URL in the user's default browser.
.br
* \f[I]false\f[]: Ask the user to copy & paste an URL from the terminal.


.SS extractor.oauth.cache
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Store tokens received during OAuth authorizations
in \f[I]cache\f[].


.SS extractor.oauth.host
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"localhost"\f[]

.IP "Description:" 4
Host name / IP address to bind to during OAuth authorization.


.SS extractor.oauth.port
.IP "Type:" 6
\f[I]integer\f[]

.IP "Default:" 9
\f[I]6414\f[]

.IP "Description:" 4
Port number to listen on during OAuth authorization.

Note: All redirects will go to port \f[I]6414\f[], regardless
of the port specified here. You'll have to manually adjust the
port number in your browser's address bar when using a different
port than the default.


.SS extractor.paheal.metadata
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Extract additional metadata (\f[I]source\f[], \f[I]uploader\f[])

Note: This requires 1 additional HTTP request per post.


.SS extractor.patreon.files
.IP "Type:" 6
\f[I]list\f[] of \f[I]strings\f[]

.IP "Default:" 9
\f[I]["images", "image_large", "attachments", "postfile", "content"]\f[]

.IP "Description:" 4
Determines types and order of files to download.

Available types:

.br
* \f[I]postfile\f[]
.br
* \f[I]images\f[]
.br
* \f[I]image_large\f[]
.br
* \f[I]attachments\f[]
.br
* \f[I]content\f[]


.SS extractor.patreon.format-images
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"download_url"\f[]

.IP "Description:" 4
Selects the format of \f[I]images\f[] \f[I]files\f[].

Possible formats:

.br
* \f[I]download_url\f[] (\f[I]"a":1,"p":1\f[])
.br
* \f[I]url\f[] (\f[I]"w":620\f[])
.br
* \f[I]original\f[] (\f[I]"q":100,"webp":0\f[])
.br
* \f[I]default\f[] (\f[I]"w":620\f[])
.br
* \f[I]default_small\f[] (\f[I]"w":360\f[])
.br
* \f[I]default_blurred\f[] (\f[I]"w":620\f[])
.br
* \f[I]default_blurred_small\f[] (\f[I]"w":360\f[])
.br
* \f[I]thumbnail\f[] (\f[I]"h":360,"w":360\f[])
.br
* \f[I]thumbnail_large\f[] (\f[I]"h":1080,"w":1080\f[])
.br
* \f[I]thumbnail_small\f[] (\f[I]"h":100,"w":100\f[])


.SS extractor.[philomena].api-key
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]null\f[]

.IP "Description:" 4
Your account's API Key,
to use your personal browsing settings and filters.


.SS extractor.[philomena].filter
.IP "Type:" 6
\f[I]integer\f[]

.IP "Default:" 9
:\f[I]derpibooru\f[]:
\f[I]56027\f[] (\f[I]Everything\f[] filter)
:\f[I]ponybooru\f[]:
\f[I]3\f[] (\f[I]Nah.\f[] filter)
:otherwise:
\f[I]2\f[]


.IP "Description:" 4
The content filter ID to use.

Setting an explicit filter ID overrides any default filters and can be used
to access 18+ content without \f[I]API Key\f[].

See \f[I]Filters\f[] for details.


.SS extractor.[philomena].svg
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Download SVG versions of images when available.

Try to download the \f[I]view_url\f[] version of these posts
when this option is disabled.


.SS extractor.pillowfort.external
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Follow links to external sites, e.g. Twitter,


.SS extractor.pillowfort.inline
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Extract inline images.


.SS extractor.pillowfort.reblogs
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Extract media from reblogged posts.


.SS extractor.pinterest.domain
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"auto"\f[]

.IP "Description:" 4
Specifies the domain used by \f[I]pinterest\f[] extractors.

Setting this option to \f[I]"auto"\f[]
uses the same domain as a given input URL.


.SS extractor.pinterest.sections
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Include pins from board sections.


.SS extractor.pinterest.stories
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Extract files from story pins.


.SS extractor.pinterest.videos
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Download from video pins.


.SS extractor.pixeldrain.api-key
.IP "Type:" 6
\f[I]string\f[]

.IP "Description:" 4
Your account's \f[I]API key\f[]


.SS extractor.pixeldrain.recursive
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Recursively download files from subfolders.


.SS extractor.pixiv.include
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]list\f[] of \f[I]strings\f[]

.IP "Default:" 9
\f[I]"artworks"\f[]

.IP "Example:" 4
.br
* "avatar,background,artworks"
.br
* ["avatar", "background", "artworks"]

.IP "Description:" 4
A (comma-separated) list of subcategories to include
when processing a user profile.

Possible values are
\f[I]"artworks"\f[],
\f[I]"avatar"\f[],
\f[I]"background"\f[],
\f[I]"favorite"\f[],
\f[I]"novel-user"\f[],
\f[I]"novel-bookmark"\f[].

It is possible to use \f[I]"all"\f[] instead of listing all values separately.


.SS extractor.pixiv.refresh-token
.IP "Type:" 6
\f[I]string\f[]

.IP "Description:" 4
The \f[I]refresh-token\f[] value you get
from running \f[I]gallery-dl oauth:pixiv\f[] (see OAuth_) or
by using a third-party tool like
\f[I]gppt\f[].


.SS extractor.pixiv.novel.covers
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Download cover images.


.SS extractor.pixiv.novel.embeds
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Download embedded images.


.SS extractor.pixiv.novel.full-series
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
When downloading a novel being part of a series,
download all novels of that series.


.SS extractor.pixiv.metadata
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Fetch extended \f[I]user\f[] metadata.


.SS extractor.pixiv.metadata-bookmark
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
For works bookmarked by
\f[I]your own account\f[],
fetch bookmark tags as \f[I]tags_bookmark\f[] metadata.

Note: This requires 1 additional API request per bookmarked post.


.SS extractor.pixiv.captions
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
For works with seemingly empty \f[I]caption\f[] metadata,
try to grab the actual \f[I]caption\f[] value using the AJAX API.


.SS extractor.pixiv.comments
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Fetch \f[I]comments\f[] metadata.

Note: This requires 1 or more additional API requests per post,
depending on the number of comments.


.SS extractor.pixiv.work.related
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Also download related artworks.


.SS extractor.pixiv.tags
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"japanese"\f[]

.IP "Description:" 4
Controls the \f[I]tags\f[] metadata field.

.br
* "japanese": List of Japanese tags
.br
* "translated": List of translated tags
.br
* "original": Unmodified list with both Japanese and translated tags


.SS extractor.pixiv.ugoira
.IP "Type:" 6
.br
* \f[I]bool\f[]
.br
* \f[I]string\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Download Pixiv's Ugoira animations.

These animations come as a \f[I].zip\f[] archive containing all
animation frames in JPEG format by default.

Set this option to \f[I]"original"\f[]
to download them as individual, higher-quality frames.

Use an ugoira post processor to convert them
to watchable animations. (Example__)


.SS extractor.pixiv.max-posts
.IP "Type:" 6
\f[I]integer\f[]

.IP "Default:" 9
\f[I]0\f[]

.IP "Description:" 4
When downloading galleries, this sets the maximum number of posts to get.
A value of \f[I]0\f[] means no limit.


.SS extractor.pixiv.sanity
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Try to fetch \f[I]limit_sanity_level\f[] works via web API.


.SS extractor.plurk.comments
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Also search Plurk comments for URLs.


.SS extractor.[postmill].save-link-post-body
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Whether or not to save the body for link/image posts.


.SS extractor.reactor.gif
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Format in which to download animated images.

Use \f[I]true\f[] to download animated images as gifs and \f[I]false\f[]
to download as mp4 videos.


.SS extractor.readcomiconline.captcha
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"stop"\f[]

.IP "Description:" 4
Controls how to handle redirects to CAPTCHA pages.

.br
* \f[I]"stop\f[]: Stop the current extractor run.
.br
* \f[I]"wait\f[]: Ask the user to solve the CAPTCHA and wait.


.SS extractor.readcomiconline.quality
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"auto"\f[]

.IP "Description:" 4
Sets the \f[I]quality\f[] query parameter of issue pages. (\f[I]"lq"\f[] or \f[I]"hq"\f[])

\f[I]"auto"\f[] uses the quality parameter of the input URL
or \f[I]"hq"\f[] if not present.


.SS extractor.reddit.comments
.IP "Type:" 6
\f[I]integer\f[]

.IP "Default:" 9
\f[I]0\f[]

.IP "Description:" 4
The value of the \f[I]limit\f[] parameter when loading
a submission and its comments.
This number (roughly) specifies the total amount of comments
being retrieved with the first API call.

Reddit's internal default and maximum values for this parameter
appear to be 200 and 500 respectively.

The value \f[I]0\f[] ignores all comments and significantly reduces the
time required when scanning a subreddit.


.SS extractor.reddit.morecomments
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Retrieve additional comments by resolving the \f[I]more\f[] comment
stubs in the base comment tree.

Note: This requires 1 additional API call for every 100 extra comments.


.SS extractor.reddit.embeds
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Download embedded comments media.


.SS extractor.reddit.date-min & .date-max
.IP "Type:" 6
\f[I]Date\f[]

.IP "Default:" 9
\f[I]0\f[] and \f[I]253402210800\f[] (timestamp of \f[I]datetime.max\f[])

.IP "Description:" 4
Ignore all submissions posted before/after this date.


.SS extractor.reddit.id-min & .id-max
.IP "Type:" 6
\f[I]string\f[]

.IP "Example:" 4
"6kmzv2"

.IP "Description:" 4
Ignore all submissions posted before/after the submission with this ID.


.SS extractor.reddit.previews
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
For failed downloads from external URLs / child extractors,
download Reddit's preview image/video if available.


.SS extractor.reddit.recursion
.IP "Type:" 6
\f[I]integer\f[]

.IP "Default:" 9
\f[I]0\f[]

.IP "Description:" 4
Reddit extractors can recursively visit other submissions
linked to in the initial set of submissions.
This value sets the maximum recursion depth.

Special values:

.br
* \f[I]0\f[]: Recursion is disabled
.br
* \f[I]-1\f[]: Infinite recursion (don't do this)


.SS extractor.reddit.refresh-token
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]null\f[]

.IP "Description:" 4
The \f[I]refresh-token\f[] value you get from
\f[I]linking your Reddit account to gallery-dl\f[].

Using a \f[I]refresh-token\f[] allows you to access private or otherwise
not publicly available subreddits, given that your account is
authorized to do so,
but requests to the reddit API are going to be rate limited
at 600 requests every 10 minutes/600 seconds.


.SS extractor.reddit.selftext
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
.br
* \f[I]true\f[] if \f[I]comments\f[] are enabled
.br
* \f[I]false\f[] otherwise

.IP "Description:" 4
Follow links in the original post's \f[I]selftext\f[].


.SS extractor.reddit.videos
.IP "Type:" 6
.br
* \f[I]bool\f[]
.br
* \f[I]string\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Control video download behavior.

.br
* \f[I]true\f[]: Download videos and use \f[I]ytdl\f[] to handle
HLS and DASH manifests
.br
* \f[I]"ytdl"\f[]: Download videos and let \f[I]ytdl\f[] handle all of
video extraction and download
.br
* \f[I]"dash"\f[]: Extract DASH manifest URLs and use \f[I]ytdl\f[]
to download and merge them. (*)
.br
* \f[I]false\f[]: Ignore videos

(*)
This saves 1 HTTP request per video
and might potentially be able to download otherwise deleted videos,
but it will not always get the best video quality available.


.SS extractor.redgifs.format
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]list\f[] of \f[I]strings\f[]

.IP "Default:" 9
\f[I]["hd", "sd", "gif"]\f[]

.IP "Description:" 4
List of names of the preferred animation format, which can be
\f[I]"hd"\f[],
\f[I]"sd"\f[],
\f[I]"gif"\f[],
\f[I]"thumbnail"\f[],
\f[I]"vthumbnail"\f[], or
\f[I]"poster"\f[].

If a selected format is not available, the next one in the list will be
tried until an available format is found.

If the format is given as \f[I]string\f[], it will be extended with
\f[I]["hd", "sd", "gif"]\f[]. Use a list with one element to
restrict it to only one possible format.


.SS extractor.rule34xyz.format
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]list\f[] of \f[I]strings\f[]

.IP "Default:" 9
\f[I]["10", "40", "41", "2"]\f[]

.IP "Example:" 4
"33,34,4"

.IP "Description:" 4
Selects the file format to extract.

When more than one format is given, the first available one is selected.


.SS extractor.sankaku.id-format
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"numeric"\f[]

.IP "Description:" 4
Format of \f[I]id\f[] metadata fields.

.br
* \f[I]"alphanumeric"\f[] or \f[I]"alnum"\f[]: 11-character alphanumeric IDs (\f[I]y0abGlDOr2o\f[])
.br
* \f[I]"numeric"\f[] or \f[I]"legacy"\f[]: numeric IDs (\f[I]360451\f[])


.SS extractor.sankaku.refresh
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Refresh download URLs before they expire.


.SS extractor.sankaku.tags
.IP "Type:" 6
.br
* \f[I]bool\f[]
.br
* \f[I]string\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Group \f[I]tags\f[] by type and
.br
provide them as \f[I]tags_TYPE\f[] and \f[I]tag_string_TYPE\f[] metadata fields,
for example \f[I]tags_artist\f[] and \f[I]tags_character\f[].
.br

\f[I]true\f[]
Enable general \f[I]tags\f[] categories

Requires:

.br
* 1 additional API request per 100 tags per post

\f[I]"extended"\f[]
Group \f[I]tags\f[] by the new, extended tag category system
used on \f[I]chan.sankakucomplex.com\f[]

Requires:

.br
* 1 additional HTTP request per post
.br
* logged-in \f[I]cookies\f[]
to fetch full \f[I]tags\f[] category data

\f[I]false\f[]
Disable \f[I]tags\f[] categories


.SS extractor.sankakucomplex.embeds
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Download video embeds from external sites.


.SS extractor.sankakucomplex.videos
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Download videos.


.SS extractor.skeb.article
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Download article images.


.SS extractor.skeb.sent-requests
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Download sent requests.


.SS extractor.skeb.thumbnails
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Download thumbnails.


.SS extractor.skeb.search.filters
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]list\f[] of \f[I]strings\f[]

.IP "Default:" 9
\f[I]["genre:art", "genre:voice", "genre:novel", "genre:video", "genre:music", "genre:correction"]\f[]

.IP "Example:" 4
"genre:music OR genre:voice"

.IP "Description:" 4
Filters used during searches.


.SS extractor.smugmug.videos
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Download video files.


.SS extractor.steamgriddb.animated
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Include animated assets when downloading from a list of assets.


.SS extractor.steamgriddb.epilepsy
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Include assets tagged with epilepsy when downloading from a list of assets.


.SS extractor.steamgriddb.dimensions
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]list\f[] of \f[I]strings\f[]

.IP "Default:" 9
\f[I]"all"\f[]

.IP "Examples:" 4
.br
* \f[I]"1024x512,512x512"\f[]
.br
* \f[I]["460x215", "920x430"]\f[]

.IP "Description:" 4
Only include assets that are in the specified dimensions. \f[I]all\f[] can be
used to specify all dimensions. Valid values are:

.br
* Grids: \f[I]460x215\f[], \f[I]920x430\f[], \f[I]600x900\f[], \f[I]342x482\f[], \f[I]660x930\f[],
\f[I]512x512\f[], \f[I]1024x1024\f[]
.br
* Heroes: \f[I]1920x620\f[], \f[I]3840x1240\f[], \f[I]1600x650\f[]
.br
* Logos: N/A (will be ignored)
.br
* Icons: \f[I]8x8\f[], \f[I]10x10\f[], \f[I]14x14\f[], \f[I]16x16\f[], \f[I]20x20\f[], \f[I]24x24\f[],
\f[I]28x28\f[], \f[I]32x32\f[], \f[I]35x35\f[], \f[I]40x40\f[], \f[I]48x48\f[], \f[I]54x54\f[],
\f[I]56x56\f[], \f[I]57x57\f[], \f[I]60x60\f[], \f[I]64x64\f[], \f[I]72x72\f[], \f[I]76x76\f[],
\f[I]80x80\f[], \f[I]90x90\f[], \f[I]96x96\f[], \f[I]100x100\f[], \f[I]114x114\f[], \f[I]120x120\f[],
\f[I]128x128\f[], \f[I]144x144\f[], \f[I]150x150\f[], \f[I]152x152\f[], \f[I]160x160\f[],
\f[I]180x180\f[], \f[I]192x192\f[], \f[I]194x194\f[], \f[I]256x256\f[], \f[I]310x310\f[],
\f[I]512x512\f[], \f[I]768x768\f[], \f[I]1024x1024\f[]


.SS extractor.steamgriddb.file-types
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]list\f[] of \f[I]strings\f[]

.IP "Default:" 9
\f[I]"all"\f[]

.IP "Examples:" 4
.br
* \f[I]"png,jpeg"\f[]
.br
* \f[I]["jpeg", "webp"]\f[]

.IP "Description:" 4
Only include assets that are in the specified file types. \f[I]all\f[] can be
used to specify all file types. Valid values are:

.br
* Grids: \f[I]png\f[], \f[I]jpeg\f[], \f[I]jpg\f[], \f[I]webp\f[]
.br
* Heroes: \f[I]png\f[], \f[I]jpeg\f[], \f[I]jpg\f[], \f[I]webp\f[]
.br
* Logos: \f[I]png\f[], \f[I]webp\f[]
.br
* Icons: \f[I]png\f[], \f[I]ico\f[]


.SS extractor.steamgriddb.download-fake-png
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Download fake PNGs alongside the real file.


.SS extractor.steamgriddb.humor
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Include assets tagged with humor when downloading from a list of assets.


.SS extractor.steamgriddb.languages
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]list\f[] of \f[I]strings\f[]

.IP "Default:" 9
\f[I]"all"\f[]

.IP "Examples:" 4
.br
* \f[I]"en,km"\f[]
.br
* \f[I]["fr", "it"]\f[]

.IP "Description:" 4
Only include assets that are in the specified languages. \f[I]all\f[] can be
used to specify all languages. Valid values are \f[I]ISO 639-1\f[]
language codes.


.SS extractor.steamgriddb.nsfw
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Include assets tagged with adult content when downloading from a list of assets.


.SS extractor.steamgriddb.sort
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]score_desc\f[]

.IP "Description:" 4
Set the chosen sorting method when downloading from a list of assets. Can be one of:

.br
* \f[I]score_desc\f[] (Highest Score (Beta))
.br
* \f[I]score_asc\f[] (Lowest Score (Beta))
.br
* \f[I]score_old_desc\f[] (Highest Score (Old))
.br
* \f[I]score_old_asc\f[] (Lowest Score (Old))
.br
* \f[I]age_desc\f[] (Newest First)
.br
* \f[I]age_asc\f[] (Oldest First)


.SS extractor.steamgriddb.static
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Include static assets when downloading from a list of assets.


.SS extractor.steamgriddb.styles
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]list\f[] of \f[I]strings\f[]

.IP "Default:" 9
\f[I]all\f[]

.IP "Examples:" 4
.br
* \f[I]white,black\f[]
.br
* \f[I]["no_logo", "white_logo"]\f[]

.IP "Description:" 4
Only include assets that are in the specified styles. \f[I]all\f[] can be used
to specify all styles. Valid values are:

.br
* Grids: \f[I]alternate\f[], \f[I]blurred\f[], \f[I]no_logo\f[], \f[I]material\f[], \f[I]white_logo\f[]
.br
* Heroes: \f[I]alternate\f[], \f[I]blurred\f[], \f[I]material\f[]
.br
* Logos: \f[I]official\f[], \f[I]white\f[], \f[I]black\f[], \f[I]custom\f[]
.br
* Icons: \f[I]official\f[], \f[I]custom\f[]


.SS extractor.steamgriddb.untagged
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Include untagged assets when downloading from a list of assets.


.SS extractor.[szurubooru].username & .token
.IP "Type:" 6
\f[I]string\f[]

.IP "Description:" 4
Username and login token of your account to access private resources.

To generate a token, visit \f[I]/user/USERNAME/list-tokens\f[]
and click \f[I]Create Token\f[].


.SS extractor.tenor.format
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]list\f[] of \f[I]strings\f[]

.IP "Default:" 9
\f[I]["gif", "mp4", "webm", "webp"]\f[]

.IP "Description:" 4
List of names of the preferred animation format.

If a selected format is not available, the next one in the list will be
tried until a format is found.

Possible formats include

.br
* \f[I]gif\f[]
.br
* \f[I]gif_transparent\f[]
.br
* \f[I]mediumgif\f[]
.br
* \f[I]gifpreview\f[]
.br
* \f[I]tinygif\f[]
.br
* \f[I]tinygif_transparent\f[]
.br
* \f[I]mp4\f[]
.br
* \f[I]tinymp4\f[]
.br
* \f[I]webm\f[]
.br
* \f[I]webp\f[]
.br
* \f[I]webp_transparent\f[]
.br
* \f[I]tinywebp\f[]
.br
* \f[I]tinywebp_transparent\f[]


.SS extractor.tiktok.audio
.IP "Type:" 6
.br
* \f[I]bool\f[]
.br
* \f[I]string\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Controls audio download behavior.

.br
* \f[I]true\f[]: Download audio tracks
.br
* \f[I]"ytdl"\f[]: Download audio tracks using \f[I]ytdl\f[]
.br
* \f[I]false\f[]: Ignore audio tracks


.SS extractor.tiktok.videos
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Download videos using \f[I]ytdl\f[].


.SS extractor.tiktok.user.avatar
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Download user avatars.


.SS extractor.tiktok.user.module
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]null\f[]

.IP "Description:" 4
Name or filesystem path of the \f[I]ytdl\f[] Python module
to extract posts from a \f[I]tiktok\f[] user profile with.

See \f[I]extractor.ytdl.module\f[].


.SS extractor.tiktok.user.tiktok-range
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]""\f[]

.IP "Example:" 4
"1-20"

.IP "Description:" 4
Range or playlist indices of \f[I]tiktok\f[] user posts to extract.

See
\f[I]ytdl/playlist_items\f[]
for details.


.SS extractor.tumblr.avatar
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Download blog avatars.


.SS extractor.tumblr.date-min & .date-max
.IP "Type:" 6
\f[I]Date\f[]

.IP "Default:" 9
\f[I]0\f[] and \f[I]null\f[]

.IP "Description:" 4
Ignore all posts published before/after this date.


.SS extractor.tumblr.external
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Follow external URLs (e.g. from "Link" posts) and try to extract
images from them.


.SS extractor.tumblr.inline
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Search posts for inline images and videos.


.SS extractor.tumblr.offset
.IP "Type:" 6
\f[I]integer\f[]

.IP "Default:" 9
\f[I]0\f[]

.IP "Description:" 4
Custom \f[I]offset\f[] starting value when paginating over blog posts.

Allows skipping over posts without having to waste API calls.


.SS extractor.tumblr.original
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Download full-resolution \f[I]photo\f[] and \f[I]inline\f[] images.

For each photo with "maximum" resolution
(width equal to 2048 or height equal to 3072)
or each inline image,
use an extra HTTP request to find the URL to its full-resolution version.


.SS extractor.tumblr.pagination
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"offset"\f[]

.IP "Description:" 4
Controls how to paginate over blog posts.

.br
* \f[I]"api"\f[]: \f[I]next\f[] parameter provided by the API
(potentially misses posts due to a
\f[I]bug\f[]
in Tumblr's API)
.br
* \f[I]"before"\f[]: timestamp of last post
.br
* \f[I]"offset"\f[]: post offset number


.SS extractor.tumblr.ratelimit
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"abort"\f[]

.IP "Description:" 4
Selects how to handle exceeding the daily API rate limit.

.br
* \f[I]"abort"\f[]: Raise an error and stop extraction
.br
* \f[I]"wait"\f[]: Wait until rate limit reset


.SS extractor.tumblr.reblogs
.IP "Type:" 6
.br
* \f[I]bool\f[]
.br
* \f[I]string\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
.br
* \f[I]true\f[]: Extract media from reblogged posts
.br
* \f[I]false\f[]: Skip reblogged posts
.br
* \f[I]"same-blog"\f[]: Skip reblogged posts unless the original post
is from the same blog


.SS extractor.tumblr.posts
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]list\f[] of \f[I]strings\f[]

.IP "Default:" 9
\f[I]"all"\f[]

.IP "Example:" 4
.br
* "video,audio,link"
.br
* ["video", "audio", "link"]

.IP "Description:" 4
A (comma-separated) list of post types to extract images, etc. from.

Possible types are \f[I]text\f[], \f[I]quote\f[], \f[I]link\f[], \f[I]answer\f[],
\f[I]video\f[], \f[I]audio\f[], \f[I]photo\f[], \f[I]chat\f[].

It is possible to use \f[I]"all"\f[] instead of listing all types separately.


.SS extractor.tumblr.fallback-delay
.IP "Type:" 6
\f[I]float\f[]

.IP "Default:" 9
\f[I]120.0\f[]

.IP "Description:" 4
Number of seconds to wait between retries
for fetching full-resolution images.


.SS extractor.tumblr.fallback-retries
.IP "Type:" 6
\f[I]integer\f[]

.IP "Default:" 9
\f[I]2\f[]

.IP "Description:" 4
Number of retries for fetching full-resolution images
or \f[I]-1\f[] for infinite retries.


.SS extractor.twibooru.api-key
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]null\f[]

.IP "Description:" 4
Your \f[I]Twibooru API Key\f[],
to use your account's browsing settings and filters.


.SS extractor.twibooru.filter
.IP "Type:" 6
\f[I]integer\f[]

.IP "Default:" 9
\f[I]2\f[] (\f[I]Everything\f[] filter)

.IP "Description:" 4
The content filter ID to use.

Setting an explicit filter ID overrides any default filters and can be used
to access 18+ content without \f[I]API Key\f[].

See \f[I]Filters\f[] for details.


.SS extractor.twibooru.svg
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Download SVG versions of images when available.

Try to download the \f[I]view_url\f[] version of these posts
when this option is disabled.


.SS extractor.twitter.ads
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Fetch media from promoted Tweets.


.SS extractor.twitter.cards
.IP "Type:" 6
.br
* \f[I]bool\f[]
.br
* \f[I]string\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Controls how to handle \f[I]Twitter Cards\f[].

.br
* \f[I]false\f[]: Ignore cards
.br
* \f[I]true\f[]: Download image content from supported cards
.br
* \f[I]"ytdl"\f[]: Additionally download video content from unsupported cards
using \f[I]ytdl\f[]


.SS extractor.twitter.cards-blacklist
.IP "Type:" 6
\f[I]list\f[] of \f[I]strings\f[]

.IP "Example:" 4
["summary", "youtube.com", "player:twitch.tv"]

.IP "Description:" 4
List of card types to ignore.

Possible values are

.br
* card names
.br
* card domains
.br
* \f[I]<card name>:<card domain>\f[]


.SS extractor.twitter.conversations
.IP "Type:" 6
.br
* \f[I]bool\f[]
.br
* \f[I]string\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
For input URLs pointing to a single Tweet,
e.g. https://twitter.com/i/web/status/<TweetID>,
fetch media from all Tweets and replies in this \f[I]conversation
<https://help.twitter.com/en/using-twitter/twitter-conversations>\f[].

If this option is equal to \f[I]"accessible"\f[],
only download from conversation Tweets
if the given initial Tweet is accessible.


.SS extractor.twitter.csrf
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"cookies"\f[]

.IP "Description:" 4
Controls how to handle Cross Site Request Forgery (CSRF) tokens.

.br
* \f[I]"auto"\f[]: Always auto-generate a token.
.br
* \f[I]"cookies"\f[]: Use token given by the \f[I]ct0\f[] cookie if present.


.SS extractor.twitter.cursor
.IP "Type:" 6
.br
* \f[I]bool\f[]
.br
* \f[I]string\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Example:" 4
"1/DAABCgABGVKi5lE___oKAAIYbfYNcxrQLggAAwAAAAIAAA"

.IP "Description:" 4
Controls from which position to start the extraction process from.

.br
* \f[I]true\f[]: Start from the beginning.
Log the most recent \f[I]cursor\f[] value when interrupted before reaching the end.
.br
* \f[I]false\f[]: Start from the beginning.
.br
* any \f[I]string\f[]: Start from the position defined by this value.

Note: A \f[I]cursor\f[] value from one timeline cannot be used with another.


.SS extractor.twitter.expand
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
For each Tweet, return *all* Tweets from that initial Tweet's
conversation or thread, i.e. *expand* all Twitter threads.

Going through a timeline with this option enabled is essentially the same
as running \f[I]gallery-dl https://twitter.com/i/web/status/<TweetID>\f[]
with enabled \f[I]conversations\f[] option
for each Tweet in said timeline.

Note: This requires at least 1 additional API call per initial Tweet.


.SS extractor.twitter.unavailable
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Try to download media marked as \f[I]Unavailable\f[],
e.g. \f[I]Geoblocked\f[] videos.


.SS extractor.twitter.include
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]list\f[] of \f[I]strings\f[]

.IP "Default:" 9
\f[I]"timeline"\f[]

.IP "Example:" 4
.br
* "avatar,background,media"
.br
* ["avatar", "background", "media"]

.IP "Description:" 4
A (comma-separated) list of subcategories to include
when processing a user profile.

Possible values are
\f[I]"info"\f[],
\f[I]"avatar"\f[],
\f[I]"background"\f[],
\f[I]"timeline"\f[],
\f[I]"tweets"\f[],
\f[I]"media"\f[],
\f[I]"replies"\f[],
\f[I]"likes"\f[].

It is possible to use \f[I]"all"\f[] instead of listing all values separately.


.SS extractor.twitter.transform
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Transform Tweet and User metadata into a simpler, uniform format.


.SS extractor.twitter.tweet-endpoint
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"auto"\f[]

.IP "Description:" 4
Selects the API endpoint used to retrieve single Tweets.

.br
* \f[I]"restid"\f[]: \f[I]/TweetResultByRestId\f[] - accessible to guest users
.br
* \f[I]"detail"\f[]: \f[I]/TweetDetail\f[] - more stable
.br
* \f[I]"auto"\f[]: \f[I]"detail"\f[] when logged in, \f[I]"restid"\f[] otherwise


.SS extractor.twitter.size
.IP "Type:" 6
\f[I]list\f[] of \f[I]strings\f[]

.IP "Default:" 9
\f[I]["orig", "4096x4096", "large", "medium", "small"]\f[]

.IP "Description:" 4
The image version to download.
Any entries after the first one will be used for potential
\f[I]fallback\f[] URLs.

Known available sizes are

.br
* \f[I]orig\f[]
.br
* \f[I]large\f[]
.br
* \f[I]medium\f[]
.br
* \f[I]small\f[]
.br
* \f[I]4096x4096\f[]
.br
* \f[I]900x900\f[]
.br
* \f[I]360x360\f[]


.SS extractor.twitter.logout
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Logout and retry as guest when access to another user's Tweets is blocked.


.SS extractor.twitter.pinned
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Fetch media from pinned Tweets.


.SS extractor.twitter.quoted
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Fetch media from quoted Tweets.

If this option is enabled, gallery-dl will try to fetch
a quoted (original) Tweet when it sees the Tweet which quotes it.


.SS extractor.twitter.ratelimit
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"wait"\f[]

.IP "Description:" 4
Selects how to handle exceeding the API rate limit.

.br
* \f[I]"abort"\f[]: Raise an error and stop extraction
.br
* \f[I]"wait"\f[]: Wait until rate limit reset
.br
* \f[I]"wait:N"\f[]: Wait for \f[I]N\f[] seconds


.SS extractor.twitter.relogin
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
When receiving a "Could not authenticate you" error while logged in with
\f[I]username & password\f[],
refresh the current login session and
try to continue from where it left off.


.SS extractor.twitter.locked
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"abort"\f[]

.IP "Description:" 4
Selects how to handle "account is temporarily locked" errors.

.br
* \f[I]"abort"\f[]: Raise an error and stop extraction
.br
* \f[I]"wait"\f[]: Wait until the account is unlocked and retry


.SS extractor.twitter.replies
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Fetch media from replies to other Tweets.

If this value is \f[I]"self"\f[], only consider replies where
reply and original Tweet are from the same user.

Note: Twitter will automatically expand conversations if you
use the \f[I]/with_replies\f[] timeline while logged in. For example,
media from Tweets which the user replied to will also be downloaded.

It is possible to exclude unwanted Tweets using \f[I]image-filter
<extractor.*.image-filter_>\f[].


.SS extractor.twitter.retweets
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Fetch media from Retweets.

If this value is \f[I]"original"\f[], metadata for these files
will be taken from the original Tweets, not the Retweets.


.SS extractor.twitter.timeline.strategy
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"auto"\f[]

.IP "Description:" 4
Controls the strategy / tweet source used for timeline URLs
(\f[I]https://twitter.com/USER/timeline\f[]).

.br
* \f[I]"tweets"\f[]: \f[I]/tweets\f[] timeline + search
.br
* \f[I]"media"\f[]: \f[I]/media\f[] timeline + search
.br
* \f[I]"with_replies"\f[]: \f[I]/with_replies\f[] timeline + search
.br
* \f[I]"auto"\f[]: \f[I]"tweets"\f[] or \f[I]"media"\f[], depending on \f[I]retweets\f[] and \f[I]text-tweets\f[] settings


.SS extractor.twitter.text-tweets
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Also emit metadata for text-only Tweets without media content.

This only has an effect with a \f[I]metadata\f[] (or \f[I]exec\f[]) post processor
with \f[I]"event": "post"\f[]
and appropriate \f[I]filename\f[].


.SS extractor.twitter.twitpic
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Extract \f[I]TwitPic\f[] embeds.


.SS extractor.twitter.unique
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Ignore previously seen Tweets.


.SS extractor.twitter.username-alt
.IP "Type:" 6
\f[I]string\f[]

.IP "Description:" 4
Alternate Identifier (username, email, phone number)
when \f[I]logging in\f[].

When not specified and asked for by Twitter,
this identifier will need to entered in an interactive prompt.


.SS extractor.twitter.users
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"user"\f[]

.IP "Example:" 4
"https://twitter.com/search?q=from:{legacy[screen_name]}"

.IP "Description:" 4
Format string for user URLs generated from
.br
\f[I]following\f[] and \f[I]list-members\f[] queries,
whose replacement field values come from Twitter \f[I]user\f[] objects
.br
(\f[I]Example\f[])

Special values:

.br
* \f[I]"user"\f[]: \f[I]https://twitter.com/i/user/{rest_id}\f[]
.br
* \f[I]"timeline"\f[]: \f[I]https://twitter.com/id:{rest_id}/timeline\f[]
.br
* \f[I]"tweets"\f[]: \f[I]https://twitter.com/id:{rest_id}/tweets\f[]
.br
* \f[I]"media"\f[]: \f[I]https://twitter.com/id:{rest_id}/media\f[]

Note: To allow gallery-dl to follow custom URL formats, set the \f[I]blacklist\f[]
for \f[I]twitter\f[] to a non-default value, e.g. an empty string \f[I]""\f[].


.SS extractor.twitter.videos
.IP "Type:" 6
.br
* \f[I]bool\f[]
.br
* \f[I]string\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Control video download behavior.

.br
* \f[I]true\f[]: Download videos
.br
* \f[I]"ytdl"\f[]: Download videos using \f[I]ytdl\f[]
.br
* \f[I]false\f[]: Skip video Tweets


.SS extractor.unsplash.format
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"raw"\f[]

.IP "Description:" 4
Name of the image format to download.

Available formats are
\f[I]"raw"\f[], \f[I]"full"\f[], \f[I]"regular"\f[], \f[I]"small"\f[], and \f[I]"thumb"\f[].


.SS extractor.vipergirls.domain
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"viper.click"\f[]

.IP "Description:" 4
Specifies the domain used by \f[I]vipergirls\f[] extractors.

For example \f[I]"viper.click"\f[] if the main domain is blocked or to bypass Cloudflare,


.SS extractor.vipergirls.like
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Automatically like posts after downloading their images.

Note: Requires \f[I]login\f[]
or \f[I]cookies\f[]


.SS extractor.vk.offset
.IP "Type:" 6
\f[I]integer\f[]

.IP "Default:" 9
\f[I]0\f[]

.IP "Description:" 4
Custom \f[I]offset\f[] starting value when paginating over image results.


.SS extractor.vsco.include
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]list\f[] of \f[I]strings\f[]

.IP "Default:" 9
\f[I]"gallery"\f[]

.IP "Example:" 4
.br
* "avatar,collection"
.br
* ["avatar", "collection"]

.IP "Description:" 4
A (comma-separated) list of subcategories to include
when processing a user profile.

Possible values are
\f[I]"avatar"\f[],
\f[I]"gallery"\f[],
\f[I]"spaces"\f[],
\f[I]"collection"\f[],

It is possible to use \f[I]"all"\f[] instead of listing all values separately.


.SS extractor.vsco.videos
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Download video files.


.SS extractor.wallhaven.api-key
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]null\f[]

.IP "Description:" 4
Your \f[I]Wallhaven API Key\f[],
to use your account's browsing settings and default filters when searching.

See https://wallhaven.cc/help/api for more information.


.SS extractor.wallhaven.include
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]list\f[] of \f[I]strings\f[]

.IP "Default:" 9
\f[I]"uploads"\f[]

.IP "Example:" 4
.br
* "uploads,collections"
.br
* ["uploads", "collections"]

.IP "Description:" 4
A (comma-separated) list of subcategories to include
when processing a user profile.

Possible values are
\f[I]"uploads"\f[], \f[I]"collections"\f[].

It is possible to use \f[I]"all"\f[] instead of listing all values separately.


.SS extractor.wallhaven.metadata
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Extract additional metadata (tags, uploader)

Note: This requires 1 additional HTTP request per post.


.SS extractor.weasyl.api-key
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]null\f[]

.IP "Description:" 4
Your \f[I]Weasyl API Key\f[],
to use your account's browsing settings and filters.


.SS extractor.weasyl.metadata
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Fetch extra submission metadata during gallery downloads.
.br
(\f[I]comments\f[], \f[I]description\f[], \f[I]favorites\f[], \f[I]folder_name\f[],
.br
\f[I]tags\f[], \f[I]views\f[])

Note: This requires 1 additional HTTP request per submission.


.SS extractor.webtoons.quality
.IP "Type:" 6
.br
* \f[I]integer\f[]
.br
* \f[I]string\f[]
.br
* \f[I]object\f[] (ext -> type)


.IP "Default:" 9
\f[I]"original"\f[]

.IP "Example:" 4
.br
* 90
.br
* "q50"
.br
* {"jpg": "q80", "jpeg": "q80", "png": false}

.IP "Description:" 4
Controls the quality of downloaded files by modifying URLs' \f[I]type\f[] parameter.

\f[I]"original"\f[]
Download minimally compressed versions of JPG files
any \f[I]integer\f[]
Use \f[I]"q<VALUE>"\f[] as \f[I]type\f[] parameter for JPEG files
any \f[I]string\f[]
Use this value as \f[I]type\f[] parameter for JPEG files
any \f[I]object\f[]
Use the given values as \f[I]type\f[] parameter for URLs with the specified extensions
.br
- Set a value to \f[I]false\f[] to completely remove these extension's \f[I]type\f[] parameter
.br
- Omit an extension to leave its URLs unchanged
.br


.SS extractor.weibo.gifs
.IP "Type:" 6
.br
* \f[I]bool\f[]
.br
* \f[I]string\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Download \f[I]gif\f[] files.

Set this to \f[I]"video"\f[] to download GIFs as video files.


.SS extractor.weibo.include
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]list\f[] of \f[I]strings\f[]

.IP "Default:" 9
\f[I]"feed"\f[]

.IP "Description:" 4
A (comma-separated) list of subcategories to include
when processing a user profile.

Possible values are
\f[I]"home"\f[],
\f[I]"feed"\f[],
\f[I]"videos"\f[],
\f[I]"newvideo"\f[],
\f[I]"article"\f[],
\f[I]"album"\f[].

It is possible to use \f[I]"all"\f[] instead of listing all values separately.


.SS extractor.weibo.livephoto
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Download \f[I]livephoto\f[] files.


.SS extractor.weibo.movies
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Download \f[I]movie\f[] videos.


.SS extractor.weibo.retweets
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Fetch media from retweeted posts.

If this value is \f[I]"original"\f[], metadata for these files
will be taken from the original posts, not the retweeted posts.


.SS extractor.weibo.videos
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Download video files.


.SS extractor.wikimedia.limit
.IP "Type:" 6
\f[I]integer\f[]

.IP "Default:" 9
\f[I]50\f[]

.IP "Description:" 4
Number of results to return in a single API query.

The value must be between 10 and 500.


.SS extractor.wikimedia.subcategories
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
For \f[I]Category:\f[] pages, recursively descent into subcategories.


.SS extractor.ytdl.cmdline-args
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]list\f[] of \f[I]strings\f[]

.IP "Example:" 4
.br
* "--quiet --write-sub --merge-output-format mkv"
.br
* ["--quiet", "--write-sub", "--merge-output-format", "mkv"]

.IP "Description:" 4
Additional \f[I]ytdl\f[] options specified as command-line arguments.

See
\f[I]yt-dlp options\f[]
/
\f[I]youtube-dl options\f[]


.SS extractor.ytdl.config-file
.IP "Type:" 6
\f[I]Path\f[]

.IP "Example:" 4
"~/.config/yt-dlp/config"

.IP "Description:" 4
Location of a \f[I]ytdl\f[] configuration file to load options from.


.SS extractor.ytdl.enabled
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Process URLs otherwise unsupported by gallery-dl with \f[I]ytdl\f[].


.SS extractor.ytdl.format
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
Default of the \f[I]ytdl\f[] \f[I]module\f[] used.
.br
(\f[I]"bestvideo*+bestaudio/best"\f[] for \f[I]yt_dlp\f[],
.br
\f[I]"bestvideo+bestaudio/best"\f[] for \f[I]youtube_dl\f[])

.IP "Description:" 4
\f[I]ytdl\f[] format selection string.

See
\f[I]yt-dlp format selection\f[]
/
\f[I]youtube-dl format selection\f[]


.SS extractor.ytdl.generic
.IP "Type:" 6
.br
* \f[I]bool\f[]
.br
* \f[I]string\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Enables the use of \f[I]ytdl's\f[] \f[I]generic\f[] extractor.

Set this option to \f[I]"force"\f[] for the same effect as
\f[I]--force-generic-extractor\f[].


.SS extractor.ytdl.logging
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Route \f[I]ytdl's\f[] output through gallery-dl's logging system.
Otherwise it will be written directly to stdout/stderr.

Note: Set \f[I]quiet\f[] and \f[I]no_warnings\f[] in
\f[I]extractor.ytdl.raw-options\f[] to \f[I]true\f[] to suppress all output.


.SS extractor.ytdl.module
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]Path\f[]

.IP "Default:" 9
\f[I]null\f[]

.IP "Example:" 4
.br
* "yt-dlp"
.br
* "/home/<USER>/.local/lib/python3.13/site-packages/youtube_dl"

.IP "Description:" 4
Name or filesystem path of the \f[I]ytdl\f[] Python module to import.

Setting this to \f[I]null\f[] will try to import \f[I]"yt_dlp"\f[]
followed by \f[I]"youtube_dl"\f[] as fallback.


.SS extractor.ytdl.raw-options
.IP "Type:" 6
\f[I]object\f[] (name -> value)

.IP "Example:" 4
.. code:: json

{
"quiet": true,
"writesubtitles": true,
"merge_output_format": "mkv"
}


.IP "Description:" 4
Additional options passed directly to the \f[I]YoutubeDL\f[] constructor.

Available options can be found in
\f[I]yt-dlp's docstrings\f[]
/
\f[I]youtube-dl's docstrings\f[]


.SS extractor.zerochan.extensions
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]list\f[] of \f[I]strings\f[]

.IP "Default:" 9
\f[I]["jpg", "png", "webp", "gif"]\f[]

.IP "Example:" 4
.br
* "gif"
.br
* ["webp", "gif", "jpg"}

.IP "Description:" 4
List of filename extensions to try when dynamically building download URLs
(\f[I]"pagination": "api"\f[] +
\f[I]"metadata": false\f[])


.SS extractor.zerochan.metadata
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Extract additional metadata (date, md5, tags, ...)

Note: This requires 1-2 additional HTTP requests per post.


.SS extractor.zerochan.pagination
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"api"\f[]

.IP "Description:" 4
Controls how to paginate over tag search results.

.br
* \f[I]"api"\f[]: Use the \f[I]JSON API\f[]
(no \f[I]extension\f[] metadata)
.br
* \f[I]"html"\f[]: Parse HTML pages
(limited to 100 pages * 24 posts)


.SS extractor.zerochan.redirects
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Automatically follow tag redirects.


.SS extractor.[booru].tags
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Group \f[I]tags\f[] by type and
provide them as \f[I]tags_<type>\f[] metadata fields,
for example \f[I]tags_artist\f[] or \f[I]tags_character\f[].

Note: This requires 1 additional HTTP request per post.


.SS extractor.[booru].notes
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Extract overlay notes (position and text).

Note: This requires 1 additional HTTP request per post.


.SS extractor.[booru].url
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]list\f[] of \f[I]strings\f[]

.IP "Default:" 9
\f[I]"file_url"\f[]

.IP "Example:" 4
.br
* "preview_url"
.br
* ["sample_url", "preview_url", "file_url"]

.IP "Description:" 4
Alternate field name to retrieve download URLs from.

When multiple names are given, download the first available one.


.SS extractor.[manga-extractor].chapter-reverse
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Reverse the order of chapter URLs extracted from manga pages.

.br
* \f[I]true\f[]: Start with the latest chapter
.br
* \f[I]false\f[]: Start with the first chapter


.SS extractor.[manga-extractor].page-reverse
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Download manga chapter pages in reverse order.


.SH DOWNLOADER OPTIONS
.SS downloader.*.enabled
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Enable/Disable this downloader module.


.SS downloader.*.filesize-min & .filesize-max
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]null\f[]

.IP "Example:" 4
"32000", "500k", "2.5M"

.IP "Description:" 4
Minimum/Maximum allowed file size in bytes.
Any file smaller/larger than this limit will not be downloaded.

Possible values are valid integer or floating-point numbers
optionally followed by one of \f[I]k\f[], \f[I]m\f[]. \f[I]g\f[], \f[I]t\f[], or \f[I]p\f[].
These suffixes are case-insensitive.


.SS downloader.*.mtime
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Use \f[I]Last-Modified\f[] HTTP response headers
to set file modification times.


.SS downloader.*.part
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Controls the use of \f[I].part\f[] files during file downloads.

.br
* \f[I]true\f[]: Write downloaded data into \f[I].part\f[] files and rename
them upon download completion. This mode additionally supports
resuming incomplete downloads.
.br
* \f[I]false\f[]: Do not use \f[I].part\f[] files and write data directly
into the actual output files.


.SS downloader.*.part-directory
.IP "Type:" 6
\f[I]Path\f[]

.IP "Default:" 9
\f[I]null\f[]

.IP "Description:" 4
Alternate location for \f[I].part\f[] files.

Missing directories will be created as needed.
If this value is \f[I]null\f[], \f[I].part\f[] files are going to be stored
alongside the actual output files.


.SS downloader.*.progress
.IP "Type:" 6
\f[I]float\f[]

.IP "Default:" 9
\f[I]3.0\f[]

.IP "Description:" 4
Number of seconds until a download progress indicator
for the current download is displayed.

Set this option to \f[I]null\f[] to disable this indicator.


.SS downloader.*.rate
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]null\f[]

.IP "Example:" 4
"32000", "500k", "2.5M"

.IP "Description:" 4
Maximum download rate in bytes per second.

Possible values are valid integer or floating-point numbers
optionally followed by one of \f[I]k\f[], \f[I]m\f[]. \f[I]g\f[], \f[I]t\f[], or \f[I]p\f[].
These suffixes are case-insensitive.


.SS downloader.*.retries
.IP "Type:" 6
\f[I]integer\f[]

.IP "Default:" 9
\f[I]extractor.*.retries\f[]

.IP "Description:" 4
Maximum number of retries during file downloads,
or \f[I]-1\f[] for infinite retries.


.SS downloader.*.timeout
.IP "Type:" 6
\f[I]float\f[]

.IP "Default:" 9
\f[I]extractor.*.timeout\f[]

.IP "Description:" 4
Connection timeout during file downloads.


.SS downloader.*.verify
.IP "Type:" 6
.br
* \f[I]bool\f[]
.br
* \f[I]string\f[]

.IP "Default:" 9
\f[I]extractor.*.verify\f[]

.IP "Description:" 4
Certificate validation during file downloads.


.SS downloader.*.proxy
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]object\f[] (scheme -> proxy)

.IP "Default:" 9
\f[I]extractor.*.proxy\f[]

.IP "Description:" 4
Proxy server used for file downloads.

Disable the use of a proxy for file downloads
by explicitly setting this option to \f[I]null\f[].


.SS downloader.http.adjust-extensions
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Check file headers of downloaded files
and adjust their filename extensions if they do not match.

For example, this will change the filename extension (\f[I]{extension}\f[])
of a file called \f[I]example.png\f[] from \f[I]png\f[] to \f[I]jpg\f[] when said file
contains JPEG/JFIF data.


.SS downloader.http.consume-content
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Controls the behavior when an HTTP response is considered
unsuccessful

If the value is \f[I]true\f[], consume the response body. This
avoids closing the connection and therefore improves connection
reuse.

If the value is \f[I]false\f[], immediately close the connection
without reading the response. This can be useful if the server
is known to send large bodies for error responses.


.SS downloader.http.chunk-size
.IP "Type:" 6
.br
* \f[I]integer\f[]
.br
* \f[I]string\f[]

.IP "Default:" 9
\f[I]32768\f[]

.IP "Example:" 4
"50k", "0.8M"

.IP "Description:" 4
Number of bytes per downloaded chunk.

Possible values are integer numbers
optionally followed by one of \f[I]k\f[], \f[I]m\f[]. \f[I]g\f[], \f[I]t\f[], or \f[I]p\f[].
These suffixes are case-insensitive.


.SS downloader.http.headers
.IP "Type:" 6
\f[I]object\f[] (name -> value)

.IP "Example:" 4
{"Accept": "image/webp,*/*", "Referer": "https://example.org/"}

.IP "Description:" 4
Additional HTTP headers to send when downloading files,


.SS downloader.http.retry-codes
.IP "Type:" 6
\f[I]list\f[] of \f[I]integers\f[]

.IP "Default:" 9
\f[I]extractor.*.retry-codes\f[]

.IP "Description:" 4
Additional \f[I]HTTP response status codes\f[]
to retry a download on.

Codes \f[I]200\f[], \f[I]206\f[], and \f[I]416\f[] (when resuming a \f[I]partial\f[]
download) will never be retried and always count as success,
regardless of this option.

\f[I]5xx\f[] codes (server error responses)  will always be retried,
regardless of this option.


.SS downloader.http.sleep-429
.IP "Type:" 6
\f[I]Duration\f[]

.IP "Default:" 9
\f[I]extractor.*.sleep-429\f[]

.IP "Description:" 4
Number of seconds to sleep when receiving a 429 Too Many Requests
response before \f[I]retrying\f[] the request.

Note: Requires
\f[I]retry-codes\f[]
to include \f[I]429\f[].


.SS downloader.http.validate
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Check for invalid responses.

Fail a download when a file does not pass
instead of downloading a potentially broken file.


.SS downloader.ytdl.cmdline-args
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]list\f[] of \f[I]strings\f[]

.IP "Example:" 4
.br
* "--quiet --write-sub --merge-output-format mkv"
.br
* ["--quiet", "--write-sub", "--merge-output-format", "mkv"]

.IP "Description:" 4
Additional \f[I]ytdl\f[] options specified as command-line arguments.

See
\f[I]yt-dlp options\f[]
/
\f[I]youtube-dl options\f[]


.SS downloader.ytdl.config-file
.IP "Type:" 6
\f[I]Path\f[]

.IP "Example:" 4
"~/.config/yt-dlp/config"

.IP "Description:" 4
Location of a \f[I]ytdl\f[] configuration file to load options from.


.SS downloader.ytdl.format
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
Default of the \f[I]ytdl\f[] \f[I]module\f[] used.
.br
(\f[I]"bestvideo*+bestaudio/best"\f[] for \f[I]yt_dlp\f[],
.br
\f[I]"bestvideo+bestaudio/best"\f[] for \f[I]youtube_dl\f[])

.IP "Description:" 4
\f[I]ytdl\f[] format selection string.

See
\f[I]yt-dlp format selection\f[]
/
\f[I]youtube-dl format selection\f[]


.SS downloader.ytdl.forward-cookies
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Forward gallery-dl's cookies to \f[I]ytdl\f[].


.SS downloader.ytdl.logging
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Route \f[I]ytdl's\f[] output through gallery-dl's logging system.
Otherwise it will be written directly to stdout/stderr.

Note: Set \f[I]quiet\f[] and \f[I]no_warnings\f[] in
\f[I]downloader.ytdl.raw-options\f[] to \f[I]true\f[] to suppress all output.


.SS downloader.ytdl.module
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]Path\f[]

.IP "Default:" 9
\f[I]null\f[]

.IP "Example:" 4
.br
* "yt-dlp"
.br
* "/home/<USER>/.local/lib/python3.13/site-packages/youtube_dl"

.IP "Description:" 4
Name or filesystem path of the \f[I]ytdl\f[] Python module to import.

Setting this to \f[I]null\f[] will try to import \f[I]"yt_dlp"\f[]
followed by \f[I]"youtube_dl"\f[] as fallback.


.SS downloader.ytdl.outtmpl
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]null\f[]

.IP "Description:" 4
The Output Template
used to generate filenames for files downloaded with \f[I]ytdl\f[].

See
\f[I]yt-dlp output template\f[]
/
\f[I]youtube-dl output template\f[].

Special values:

.br
* \f[I]null\f[]: generate filenames with \f[I]extractor.*.filename\f[]
.br
* \f[I]"default"\f[]: use \f[I]ytdl's\f[] default, currently
\f[I]"%(title)s [%(id)s].%(ext)s"\f[] for \f[I]yt-dlp\f[] /
\f[I]"%(title)s-%(id)s.%(ext)s"\f[] for \f[I]youtube-dl\f[]

Note: An output template other than \f[I]null\f[] might
cause unexpected results in combination with certain options
(e.g. \f[I]"skip": "enumerate"\f[])


.SS downloader.ytdl.raw-options
.IP "Type:" 6
\f[I]object\f[] (name -> value)

.IP "Example:" 4
.. code:: json

{
"quiet": true,
"writesubtitles": true,
"merge_output_format": "mkv"
}


.IP "Description:" 4
Additional options passed directly to the \f[I]YoutubeDL\f[] constructor.

Available options can be found in
\f[I]yt-dlp's docstrings\f[]
/
\f[I]youtube-dl's docstrings\f[]


.SH OUTPUT OPTIONS
.SS output.mode
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]object\f[] (key -> format string)

.IP "Default:" 9
\f[I]"auto"\f[]

.IP "Description:" 4
Controls the output string format and status indicators.

.br
* \f[I]"null"\f[]: No output
.br
* \f[I]"pipe"\f[]: Suitable for piping to other processes or files
.br
* \f[I]"terminal"\f[]: Suitable for the standard Windows console
.br
* \f[I]"color"\f[]: Suitable for terminals that understand ANSI escape codes and colors
.br
* \f[I]"auto"\f[]: \f[I]"terminal"\f[] on Windows with \f[I]output.ansi\f[] disabled,
\f[I]"color"\f[] otherwise.

It is possible to use custom output format strings
.br
by setting this option to an \f[I]object\f[] and specifying
\f[I]start\f[], \f[I]success\f[], \f[I]skip\f[], \f[I]progress\f[], and \f[I]progress-total\f[].
.br

For example, the following will replicate the same output as \f[I]mode: color\f[]:

.. code:: json

{
"start"  : "{}",
"success": "\\r\\u001b[1;32m{}\\u001b[0m\\n",
"skip"   : "\\u001b[2m{}\\u001b[0m\\n",
"progress"      : "\\r{0:>7}B {1:>7}B/s ",
"progress-total": "\\r{3:>3}% {0:>7}B {1:>7}B/s "
}

\f[I]start\f[], \f[I]success\f[], and \f[I]skip\f[] are used to output the current
filename, where \f[I]{}\f[] or \f[I]{0}\f[] is replaced with said filename.
If a given format string contains printable characters other than that,
their number needs to be specified as \f[I][<number>, <format string>]\f[]
to get the correct results for \f[I]output.shorten\f[]. For example

.. code:: json

"start"  : [12, "Downloading {}"]

\f[I]progress\f[] and \f[I]progress-total\f[] are used when displaying the
.br
\f[I]download progress indicator\f[],
\f[I]progress\f[] when the total number of bytes to download is unknown,
.br
\f[I]progress-total\f[] otherwise.

For these format strings

.br
* \f[I]{0}\f[] is number of bytes downloaded
.br
* \f[I]{1}\f[] is number of downloaded bytes per second
.br
* \f[I]{2}\f[] is total number of bytes
.br
* \f[I]{3}\f[] is percent of bytes downloaded to total bytes


.SS output.stdout & .stdin & .stderr
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]object\f[]

.IP "Example:" 4
.. code:: json

"utf-8"

.. code:: json

{
"encoding": "utf-8",
"errors": "replace",
"line_buffering": true
}


.IP "Description:" 4
\f[I]Reconfigure\f[]
a \f[I]standard stream\f[].

Possible options are

.br
* \f[I]encoding\f[]
.br
* \f[I]errors\f[]
.br
* \f[I]newline\f[]
.br
* \f[I]line_buffering\f[]
.br
* \f[I]write_through\f[]

When this option is specified as a simple \f[I]string\f[],
it is interpreted as \f[I]{"encoding": "<string-value>", "errors": "replace"}\f[]

Note: \f[I]errors\f[] always defaults to \f[I]"replace"\f[]


.SS output.shorten
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Controls whether the output strings should be shortened to fit
on one console line.

Set this option to \f[I]"eaw"\f[] to also work with east-asian characters
with a display width greater than 1.


.SS output.colors
.IP "Type:" 6
\f[I]object\f[] (key -> ANSI color)

.IP "Default:" 9
.. code:: json

{
"success": "1;32",
"skip"   : "2",
"debug"  : "0;37",
"info"   : "1;37",
"warning": "1;33",
"error"  : "1;31"
}


.IP "Description:" 4
Controls the
\f[I]ANSI colors\f[]
used for various outputs.

Output for \f[I]mode: color\f[]

.br
* \f[I]success\f[]: successfully downloaded files
.br
* \f[I]skip\f[]: skipped files

Logging Messages:

.br
* \f[I]debug\f[]: debug logging messages
.br
* \f[I]info\f[]: info logging messages
.br
* \f[I]warning\f[]: warning logging messages
.br
* \f[I]error\f[]: error logging messages


.SS output.ansi
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
On Windows, enable ANSI escape sequences and colored output
.br
by setting the \f[I]ENABLE_VIRTUAL_TERMINAL_PROCESSING\f[] flag for stdout and stderr.
.br


.SS output.skip
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Show skipped file downloads.


.SS output.fallback
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Include fallback URLs in the output of \f[I]-g/--get-urls\f[].


.SS output.private
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Include private fields,
i.e. fields whose name starts with an underscore,
in the output of \f[I]-K/--list-keywords\f[] and \f[I]-j/--dump-json\f[].


.SS output.progress
.IP "Type:" 6
.br
* \f[I]bool\f[]
.br
* \f[I]string\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Controls the progress indicator when *gallery-dl* is run with
multiple URLs as arguments.

.br
* \f[I]true\f[]: Show the default progress indicator
(\f[I]"[{current}/{total}] {url}"\f[])
.br
* \f[I]false\f[]: Do not show any progress indicator
.br
* Any \f[I]string\f[]: Show the progress indicator using this
as a custom \f[I]format string\f[]. Possible replacement keys are
\f[I]current\f[], \f[I]total\f[]  and \f[I]url\f[].


.SS output.log
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]Logging Configuration\f[]

.IP "Default:" 9
\f[I]"[{name}][{levelname}] {message}"\f[]

.IP "Description:" 4
Configuration for logging output to stderr.

If this is a simple \f[I]string\f[], it specifies
the format string for logging messages.


.SS output.logfile
.IP "Type:" 6
.br
* \f[I]Path\f[]
.br
* \f[I]Logging Configuration\f[]

.IP "Description:" 4
File to write logging output to.


.SS output.unsupportedfile
.IP "Type:" 6
.br
* \f[I]Path\f[]
.br
* \f[I]Logging Configuration\f[]

.IP "Description:" 4
File to write external URLs unsupported by *gallery-dl* to.

The default format string here is \f[I]"{message}"\f[].


.SS output.errorfile
.IP "Type:" 6
.br
* \f[I]Path\f[]
.br
* \f[I]Logging Configuration\f[]

.IP "Description:" 4
File to write input URLs which returned an error to.

The default format string here is also \f[I]"{message}"\f[].

When combined with
\f[I]-I\f[]/\f[I]--input-file-comment\f[] or
\f[I]-x\f[]/\f[I]--input-file-delete\f[],
this option will cause *all* input URLs from these files
to be commented/deleted after processing them
and not just successful ones.


.SS output.num-to-str
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Convert numeric values (\f[I]integer\f[] or \f[I]float\f[]) to \f[I]string\f[]
before outputting them as JSON.


.SH POSTPROCESSOR OPTIONS
.SS classify.mapping
.IP "Type:" 6
\f[I]object\f[] (directory -> extensions)

.IP "Default:" 9
.. code:: json

{
"Pictures" : ["jpg", "jpeg", "png", "gif", "bmp", "svg", "webp",
"avif", "heic", "heif", "ico", "psd"],
"Video"    : ["flv", "ogv", "avi", "mp4", "mpg", "mpeg", "3gp", "mkv",
"webm", "vob", "wmv", "m4v", "mov"],
"Music"    : ["mp3", "aac", "flac", "ogg", "wma", "m4a", "wav"],
"Archives" : ["zip", "rar", "7z", "tar", "gz", "bz2"],
"Documents": ["txt", "pdf"]
}


.IP "Description:" 4
A mapping from directory names to filename extensions that should
be stored in them.

Files with an extension not listed will be ignored and stored
in their default location.


.SS compare.action
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"replace"\f[]

.IP "Description:" 4
The action to take when files do **not** compare as equal.

.br
* \f[I]"replace"\f[]: Replace/Overwrite the old version with the new one

.br
* \f[I]"enumerate"\f[]: Add an enumeration index to the filename of the new
version like \f[I]skip = "enumerate"\f[]


.SS compare.equal
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"null"\f[]

.IP "Description:" 4
The action to take when files do compare as equal.

.br
* \f[I]"abort:N"\f[]: Stop the current extractor run
after \f[I]N\f[] consecutive files compared as equal.

.br
* \f[I]"terminate:N"\f[]: Stop the current extractor run,
including parent extractors,
after \f[I]N\f[] consecutive files compared as equal.

.br
* \f[I]"exit:N"\f[]: Exit the program
after \f[I]N\f[] consecutive files compared as equal.


.SS compare.shallow
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Only compare file sizes. Do not read and compare their content.


.SS directory.event
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]list\f[] of \f[I]strings\f[]

.IP "Default:" 9
\f[I]"prepare"\f[]

.IP "Description:" 4
The event(s) for which \f[I]directory\f[] format strings are (re)evaluated.

See \f[I]metadata.event\f[] for a list of available events.


.SS exec.archive
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]Path\f[]

.IP "Description:" 4
Database to store IDs of executed commands in,
similar to \f[I]extractor.*.archive\f[].

The following archive options are also supported:

.br
* \f[I]archive-format\f[]
.br
* \f[I]archive-prefix\f[]
.br
* \f[I]archive-pragma\f[]
.br
* \f[I]archive-table \f[]


.SS exec.async
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Controls whether to wait for a subprocess to finish
or to let it run asynchronously.


.SS exec.command
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]list\f[] of \f[I]strings\f[]

.IP "Example:" 4
.br
* "convert {} {}.png && rm {}"
.br
* ["echo", "{user[account]}", "{id}"]

.IP "Description:" 4
The command to run.

.br
* If this is a \f[I]string\f[], it will be executed using the system's
shell, e.g. \f[I]/bin/sh\f[]. Any \f[I]{}\f[] will be replaced
with the full path of a file or target directory, depending on
\f[I]exec.event\f[]

.br
* If this is a \f[I]list\f[], the first element specifies the program
name and any further elements its arguments.
Each element of this list is treated as a \f[I]format string\f[] using
the files' metadata as well as \f[I]{_path}\f[], \f[I]{_directory}\f[],
and \f[I]{_filename}\f[].


.SS exec.event
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]list\f[] of \f[I]strings\f[]

.IP "Default:" 9
\f[I]"after"\f[]

.IP "Description:" 4
The event(s) for which \f[I]exec.command\f[] is run.

See \f[I]metadata.event\f[] for a list of available events.


.SS hash.chunk-size
.IP "Type:" 6
\f[I]integer\f[]

.IP "Default:" 9
\f[I]32768\f[]

.IP "Description:" 4
Number of bytes read per chunk during file hash computation.


.SS hash.event
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]list\f[] of \f[I]strings\f[]

.IP "Default:" 9
\f[I]"file"\f[]

.IP "Description:" 4
The event(s) for which \f[I]file hashes\f[] are computed.

See \f[I]metadata.event\f[] for a list of available events.


.SS hash.filename
.IP "Type:" 6
.br
* \f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Rebuild \f[I]filenames\f[] after computing
\f[I]hash digests\f[] and adding them to the metadata dict.


.SS hash.hashes
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]object\f[] (field name -> hash algorithm)

.IP "Default:" 9
\f[I]"md5,sha1"\f[]

.IP "Example:" 4
.. code:: json

"sha256:hash_sha,sha3_512:hash_sha3"

.. code:: json

{
"hash_sha" : "sha256",
"hash_sha3": "sha3_512"
}


.IP "Description:" 4
Hash digests to compute.

For a list of available hash algorithms, run

.. code::

python -c "import hashlib; print('\\n'.join(hashlib.algorithms_available))"

or see \f[I]python/hashlib\f[].

.br
* If this is a \f[I]string\f[],
it is parsed as a a comma-separated list of algorthm-fieldname pairs:

.. code::

[<hash algorithm> ":"] <field name> ["," ...]

When \f[I]<hash algorithm>\f[] is omitted,
\f[I]<field name>\f[] is used as algorithm name.

.br
* If this is an \f[I]object\f[],
it is a \f[I]<field name>\f[] to \f[I]<algorithm name>\f[] mapping
for hash digests to compute.


.SS metadata.mode
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"json"\f[]

.IP "Description:" 4
Selects how to process metadata.

.br
* \f[I]"json"\f[]: write metadata using \f[I]json.dump()\f[]
.br
* \f[I]"jsonl"\f[]: write metadata in \f[I]JSON Lines
<https://jsonlines.org/>\f[] format
.br
* \f[I]"tags"\f[]: write \f[I]tags\f[] separated by newlines
.br
* \f[I]"custom"\f[]: write the result of applying \f[I]metadata.content-format\f[]
to a file's metadata dictionary
.br
* \f[I]"modify"\f[]: add or modify metadata entries
.br
* \f[I]"delete"\f[]: remove metadata entries


.SS metadata.filename
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]null\f[]

.IP "Example:" 4
"{id}.data.json"

.IP "Description:" 4
A \f[I]format string\f[] to build the filenames for metadata files with.
(see \f[I]extractor.filename\f[])

Using \f[I]"-"\f[] as filename will write all output to \f[I]stdout\f[].

If this option is set, \f[I]metadata.extension\f[] and
\f[I]metadata.extension-format\f[] will be ignored.


.SS metadata.directory
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]list\f[] of \f[I]strings\f[]

.IP "Default:" 9
\f[I]"."\f[]

.IP "Example:" 4
.br
* "metadata"
.br
* ["..", "metadata", "\\fF {id // 500 * 500}"]

.IP "Description:" 4
Directory where metadata files are stored in
relative to \f[I]metadata.base-directory\f[].


.SS metadata.base-directory
.IP "Type:" 6
.br
* \f[I]bool\f[]
.br
* \f[I]Path\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Selects the relative location for metadata files.

.br
* \f[I]false\f[]: current target location for file downloads (\f[I]base-directory\f[] + directory_)
.br
* \f[I]true\f[]: current \f[I]base-directory\f[] location
.br
* any \f[I]Path\f[]: custom location


.SS metadata.extension
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"json"\f[] or \f[I]"txt"\f[]

.IP "Description:" 4
Filename extension for metadata files that will be appended to the
original file names.


.SS metadata.extension-format
.IP "Type:" 6
\f[I]string\f[]

.IP "Example:" 4
.br
* "{extension}.json"
.br
* "json"

.IP "Description:" 4
Custom format string to build filename extensions for metadata
files with, which will replace the original filename extensions.

Note: \f[I]metadata.extension\f[] is ignored if this option is set.


.SS metadata.metadata-path
.IP "Type:" 6
\f[I]string\f[]

.IP "Example:" 4
"_meta_path"

.IP "Description:" 4
Insert the path of generated files
into metadata dictionaries as the given name.


.SS metadata.event
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]list\f[] of \f[I]strings\f[]

.IP "Default:" 9
\f[I]"file"\f[]

.IP "Example:" 4
.br
* "prepare,file,after"
.br
* ["prepare-after", "skip"]

.IP "Description:" 4
The event(s) for which metadata gets written to a file.

Available events are:

\f[I]init\f[]
After post processor initialization
and before the first file download
\f[I]finalize\f[]
On extractor shutdown, e.g. after all files were downloaded
\f[I]finalize-success\f[]
On extractor shutdown when no error occurred
\f[I]finalize-error\f[]
On extractor shutdown when at least one error occurred
\f[I]prepare\f[]
Before a file download
\f[I]prepare-after\f[]
Before a file download,
but after building and checking file paths
\f[I]file\f[]
When completing a file download,
but before it gets moved to its target location
\f[I]after\f[]
After a file got moved to its target location
\f[I]skip\f[]
When skipping a file download
\f[I]error\f[]
After a file download failed
\f[I]post\f[]
When starting to download all files of a post,
e.g. a Tweet on Twitter or a post on Patreon.
\f[I]post-after\f[]
After downloading all files of a post


.SS metadata.include
.IP "Type:" 6
\f[I]list\f[] of \f[I]strings\f[]

.IP "Example:" 4
["id", "width", "height", "description"]

.IP "Description:" 4
Include only the given top-level keys when writing JSON data.

Note: Missing or undefined fields will be silently ignored.


.SS metadata.exclude
.IP "Type:" 6
\f[I]list\f[] of \f[I]strings\f[]

.IP "Example:" 4
["blocked", "watching", "status"]

.IP "Description:" 4
Exclude all given keys from written JSON data.

Note: Cannot be used with \f[I]metadata.include\f[].


.SS metadata.fields
.IP "Type:" 6
.br
* \f[I]list\f[] of \f[I]strings\f[]
.br
* \f[I]object\f[] (field name -> \f[I]format string\f[])

.IP "Example:" 4
.. code:: json

["blocked", "watching", "status[creator][name]"]

.. code:: json

{
"blocked"         : "***",
"watching"        : "\\fE 'yes' if watching else 'no'",
"status[username]": "{status[creator][name]!l}"
}


.IP "Description:" 4
.br
* \f[I]"mode": "delete"\f[]:
A list of metadata field names to remove.
.br
* \f[I]"mode": "modify"\f[]:
An object with metadata field names mapping to a \f[I]format string\f[]
whose result is assigned to said field name.


.SS metadata.content-format
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]list\f[] of \f[I]strings\f[]

.IP "Example:" 4
.br
* "tags:\\n\\n{tags:J\\n}\\n"
.br
* ["tags:", "", "{tags:J\\n}"]

.IP "Description:" 4
Custom format string to build the content of metadata files with.

Note: Only applies for \f[I]"mode": "custom"\f[].


.SS metadata.ascii
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Escape all non-ASCII characters.

See the \f[I]ensure_ascii\f[] argument of \f[I]json.dump()\f[] for further details.

Note: Only applies for \f[I]"mode": "json"\f[] and \f[I]"jsonl"\f[].


.SS metadata.indent
.IP "Type:" 6
.br
* \f[I]integer\f[]
.br
* \f[I]string\f[]

.IP "Default:" 9
\f[I]4\f[]

.IP "Description:" 4
Indentation level of JSON output.

See the \f[I]indent\f[] argument of \f[I]json.dump()\f[] for further details.

Note: Only applies for \f[I]"mode": "json"\f[].


.SS metadata.separators
.IP "Type:" 6
\f[I]list\f[] with two \f[I]string\f[] elements

.IP "Default:" 9
\f[I][", ", ": "]\f[]

.IP "Description:" 4
\f[I]<item separator>\f[] - \f[I]<key separator>\f[] pair
to separate JSON keys and values with.

See the \f[I]separators\f[] argument of \f[I]json.dump()\f[] for further details.

Note: Only applies for \f[I]"mode": "json"\f[] and \f[I]"jsonl"\f[].


.SS metadata.sort
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Sort output by key.

See the \f[I]sort_keys\f[] argument of \f[I]json.dump()\f[] for further details.

Note: Only applies for \f[I]"mode": "json"\f[] and \f[I]"jsonl"\f[].


.SS metadata.open
.IP "Type:" 6
\f[I]string\f[]

.IP "Defsult:" 4
\f[I]"w"\f[]

.IP "Description:" 4
The \f[I]mode\f[] in which metadata files get opened.

For example,
use \f[I]"a"\f[] to append to a file's content
or \f[I]"w"\f[] to truncate it.

See the \f[I]mode\f[] argument of \f[I]open()\f[] for further details.


.SS metadata.encoding
.IP "Type:" 6
\f[I]string\f[]

.IP "Defsult:" 4
\f[I]"utf-8"\f[]

.IP "Description:" 4
Name of the encoding used to encode a file's content.

See the \f[I]encoding\f[] argument of \f[I]open()\f[] for further details.


.SS metadata.private
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Include private fields,
i.e. fields whose name starts with an underscore.


.SS metadata.skip
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Do not overwrite already existing files.


.SS metadata.archive
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]Path\f[]

.IP "Description:" 4
Database to store IDs of generated metadata files in,
similar to \f[I]extractor.*.archive\f[].

The following archive options are also supported:

.br
* \f[I]archive-format\f[]
.br
* \f[I]archive-prefix\f[]
.br
* \f[I]archive-pragma\f[]
.br
* \f[I]archive-table \f[]


.SS metadata.mtime
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Set modification times of generated metadata files
according to the accompanying downloaded file.

Enabling this option will only have an effect
*if* there is actual \f[I]mtime\f[] metadata available, that is

.br
* after a file download (\f[I]"event": "file"\f[] (default), \f[I]"event": "after"\f[])
.br
* when running *after* an \f[I]mtime\f[] post processes for the same \f[I]event\f[]

For example, a \f[I]metadata\f[] post processor for \f[I]"event": "post"\f[] will
*not* be able to set its file's modification time unless an \f[I]mtime\f[]
post processor with \f[I]"event": "post"\f[] runs *before* it.


.SS mtime.event
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]list\f[] of \f[I]strings\f[]

.IP "Default:" 9
\f[I]"file"\f[]

.IP "Description:" 4
The event(s) for which \f[I]mtime.key\f[] or \f[I]mtime.value\f[] get evaluated.

See \f[I]metadata.event\f[] for a list of available events.


.SS mtime.key
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"date"\f[]

.IP "Description:" 4
Name of the metadata field whose value should be used.

This value must be either a UNIX timestamp or a
\f[I]datetime\f[] object.

Note: This option gets ignored if \f[I]mtime.value\f[] is set.


.SS mtime.value
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]null\f[]

.IP "Example:" 4
.br
* "{status[date]}"
.br
* "{content[0:6]:R22/2022/D%Y%m%d/}"

.IP "Description:" 4
A \f[I]format string\f[] whose value should be used.

The resulting value must be either a UNIX timestamp or a
\f[I]datetime\f[] object.


.SS python.archive
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]Path\f[]

.IP "Description:" 4
Database to store IDs of called Python functions in,
similar to \f[I]extractor.*.archive\f[].

The following archive options are also supported:

.br
* \f[I]archive-format\f[]
.br
* \f[I]archive-prefix\f[]
.br
* \f[I]archive-pragma\f[]
.br
* \f[I]archive-table \f[]


.SS python.event
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]list\f[] of \f[I]strings\f[]

.IP "Default:" 9
\f[I]"file"\f[]

.IP "Description:" 4
The event(s) for which \f[I]python.function\f[] gets called.

See \f[I]metadata.event\f[] for a list of available events.


.SS python.function
.IP "Type:" 6
\f[I]string\f[]

.IP "Example:" 4
.br
* "my_module:generate_text"
.br
* "~/.local/share/gdl-utils.py:resize"

.IP "Description:" 4
The Python function to call.

This function is specified as \f[I]<module>:<function name>\f[]
and gets called with the current metadata dict as argument.

\f[I]module\f[] is either an importable Python module name
or the \f[I]Path\f[] to a .py file,


.SS rename.from
.IP "Type:" 6
\f[I]string\f[]

.IP "Description:" 4
The \f[I]format string\f[] for filenames to rename.

When no value is given, \f[I]extractor.*.filename\f[] is used.


.SS rename.to
.IP "Type:" 6
\f[I]string\f[]

.IP "Description:" 4
The \f[I]format string\f[] for target filenames.

When no value is given, \f[I]extractor.*.filename\f[] is used.


.SS rename.skip
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Do not rename a file when another file with the target name already exists.


.SS ugoira.extension
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"webm"\f[]

.IP "Description:" 4
Filename extension for the resulting video files.


.SS ugoira.ffmpeg-args
.IP "Type:" 6
\f[I]list\f[] of \f[I]strings\f[]

.IP "Default:" 9
\f[I]null\f[]

.IP "Example:" 4
["-c:v", "libvpx-vp9", "-an", "-b:v", "2M"]

.IP "Description:" 4
Additional \f[I]ffmpeg\f[] command-line arguments.


.SS ugoira.ffmpeg-demuxer
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]auto\f[]

.IP "Description:" 4
\f[I]ffmpeg\f[] demuxer to read and process input files with.

Possible values are

.br
* "\f[I]concat\f[]" (inaccurate frame timecodes for non-uniform frame delays)
.br
* "\f[I]image2\f[]" (accurate timecodes, requires nanosecond file timestamps, i.e. no Windows or macOS)
.br
* "mkvmerge" (accurate timecodes, only WebM or MKV, requires \f[I]mkvmerge\f[])
.br
* "archive" (store "original" frames in a \f[I].zip\f[] archive)

"auto" will select mkvmerge if available and fall back to concat otherwise.


.SS ugoira.ffmpeg-location
.IP "Type:" 6
\f[I]Path\f[]

.IP "Default:" 9
\f[I]"ffmpeg"\f[]

.IP "Description:" 4
Location of the \f[I]ffmpeg\f[] (or \f[I]avconv\f[]) executable to use.


.SS ugoira.mkvmerge-location
.IP "Type:" 6
\f[I]Path\f[]

.IP "Default:" 9
\f[I]"mkvmerge"\f[]

.IP "Description:" 4
Location of the \f[I]mkvmerge\f[] executable for use with the
\f[I]mkvmerge demuxer\f[].


.SS ugoira.ffmpeg-output
.IP "Type:" 6
.br
* \f[I]bool\f[]
.br
* \f[I]string\f[]

.IP "Default:" 9
\f[I]"error"\f[]

.IP "Description:" 4
Controls \f[I]ffmpeg\f[] output.

.br
* \f[I]true\f[]: Enable \f[I]ffmpeg\f[] output
.br
* \f[I]false\f[]: Disable all \f[I]ffmpeg\f[] output
.br
* any \f[I]string\f[]: Pass \f[I]-hide_banner\f[] and \f[I]-loglevel\f[]
with this value as argument to \f[I]ffmpeg\f[]


.SS ugoira.ffmpeg-twopass
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Enable Two-Pass encoding.


.SS ugoira.framerate
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"auto"\f[]

.IP "Description:" 4
Controls the frame rate argument (\f[I]-r\f[]) for \f[I]ffmpeg\f[]

.br
* \f[I]"auto"\f[]: Automatically assign a fitting frame rate
based on delays between frames.
.br
* \f[I]"uniform"\f[]: Like \f[I]auto\f[], but assign an explicit frame rate
only to Ugoira with uniform frame delays.
.br
* any other \f[I]string\f[]:  Use this value as argument for \f[I]-r\f[].
.br
* \f[I]null\f[] or an empty \f[I]string\f[]: Don't set an explicit frame rate.


.SS ugoira.keep-files
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Keep ZIP archives after conversion.


.SS ugoira.libx264-prevent-odd
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Prevent \f[I]"width/height not divisible by 2"\f[] errors
when using \f[I]libx264\f[] or \f[I]libx265\f[] encoders
by applying a simple cropping filter. See this \f[I]Stack Overflow
thread\f[]
for more information.

This option, when \f[I]libx264/5\f[] is used, automatically
adds \f[I]["-vf", "crop=iw-mod(iw\\\\,2):ih-mod(ih\\\\,2)"]\f[]
to the list of \f[I]ffmpeg\f[] command-line arguments
to reduce an odd width/height by 1 pixel and make them even.


.SS ugoira.metadata
.IP "Type:" 6
.br
* \f[I]bool\f[]
.br
* \f[I]string\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
When using \f[I]"mode": "archive"\f[], save Ugoira frame delay data as
\f[I]animation.json\f[] within the archive file.

If this is a \f[I]string\f[],
use it as alternate filename for frame delay files.


.SS ugoira.mtime
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Set modification times of generated ugoira aniomations.


.SS ugoira.repeat-last-frame
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Allow repeating the last frame when necessary
to prevent it from only being displayed for a very short amount of time.


.SS ugoira.skip
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Do not convert frames if target file already exists.


.SS zip.compression
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"store"\f[]

.IP "Description:" 4
Compression method to use when writing the archive.

Possible values are \f[I]"store"\f[], \f[I]"zip"\f[], \f[I]"bzip2"\f[], \f[I]"lzma"\f[].


.SS zip.extension
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"zip"\f[]

.IP "Description:" 4
Filename extension for the created ZIP archive.


.SS zip.files
.IP "Type:" 6
\f[I]list\f[] of \f[I]Path\f[]

.IP "Example:" 4
["info.json"]

.IP "Description:" 4
List of extra files to be added to a ZIP archive.

Note: Relative paths are relative to the current
\f[I]download directory\f[].


.SS zip.keep-files
.IP "Type:" 6
\f[I]bool\f[]

.IP "Default:" 9
\f[I]false\f[]

.IP "Description:" 4
Keep the actual files after writing them to a ZIP archive.


.SS zip.mode
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"default"\f[]

.IP "Description:" 4
.br
* \f[I]"default"\f[]: Write the central directory file header
once after everything is done or an exception is raised.

.br
* \f[I]"safe"\f[]: Update the central directory file header
each time a file is stored in a ZIP archive.

This greatly reduces the chance a ZIP archive gets corrupted in
case the Python interpreter gets shut down unexpectedly
(power outage, SIGKILL) but is also a lot slower.


.SH MISCELLANEOUS OPTIONS
.SS extractor.modules
.IP "Type:" 6
\f[I]list\f[] of \f[I]strings\f[]

.IP "Default:" 9
The \f[I]modules\f[] list in
\f[I]extractor/__init__.py\f[]

.IP "Example:" 4
["reddit", "danbooru", "mangadex"]

.IP "Description:" 4
List of internal modules to load when searching for a suitable
extractor class. Useful to reduce startup time and memory usage.


.SS extractor.module-sources
.IP "Type:" 6
\f[I]list\f[] of \f[I]Path\f[] instances

.IP "Example:" 4
["~/.config/gallery-dl/modules", null]

.IP "Description:" 4
List of directories to load external extractor modules from.

Any file in a specified directory with a \f[I].py\f[] filename extension
gets \f[I]imported\f[]
and searched for potential extractors,
i.e. classes with a \f[I]pattern\f[] attribute.

Note: \f[I]null\f[] references internal extractors defined in
\f[I]extractor/__init__.py\f[]
or by \f[I]extractor.modules\f[].


.SS globals
.IP "Type:" 6
.br
* \f[I]Path\f[]
.br
* \f[I]string\f[]

.IP "Example:" 4
.br
* "~/.local/share/gdl-globals.py"
.br
* "gdl-globals"

.IP "Description:" 4
Path to or name of an
.br
\f[I]importable\f[]
Python module,
whose namespace,
.br
in addition to the \f[I]GLOBALS\f[] dict in
\f[I]util.py\f[],
gets used as \f[I]globals parameter\f[] for compiled Python expressions.


.SS cache.file
.IP "Type:" 6
\f[I]Path\f[]

.IP "Default:" 9
.br
* (\f[I]%APPDATA%\f[] or \f[I]"~"\f[]) + \f[I]"/gallery-dl/cache.sqlite3"\f[] on Windows
.br
* (\f[I]$XDG_CACHE_HOME\f[] or \f[I]"~/.cache"\f[]) + \f[I]"/gallery-dl/cache.sqlite3"\f[] on all other platforms

.IP "Description:" 4
Path of the SQLite3 database used to cache login sessions,
cookies and API tokens across gallery-dl invocations.

Set this option to \f[I]null\f[] or an invalid path to disable
this cache.


.SS filters-environment
.IP "Type:" 6
.br
* \f[I]bool\f[]
.br
* \f[I]string\f[]

.IP "Default:" 9
\f[I]true\f[]

.IP "Description:" 4
Evaluate filter expressions in a special environment
preventing them from raising fatal exceptions.

\f[I]true\f[] or \f[I]"tryexcept"\f[]:
Wrap expressions in a try/except block;
Evaluate expressions raising an exception as \f[I]false\f[]
\f[I]false\f[] or \f[I]"raw"\f[]:
Do not wrap expressions in a special environment
\f[I]"defaultdict"\f[]:
Prevent exceptions when accessing undefined variables
by using a \f[I]defaultdict\f[]


.SS format-separator
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"/"\f[]

.IP "Description:" 4
Character(s) used as argument separator in format string
\f[I]format specifiers\f[].

For example, setting this option to \f[I]"#"\f[] would allow a replacement
operation to be \f[I]Rold#new#\f[] instead of the default \f[I]Rold/new/\f[]


.SS input-files
.IP "Type:" 6
\f[I]list\f[] of \f[I]Path\f[]

.IP "Example:" 4
["~/urls.txt", "$HOME/input"]

.IP "Description:" 4
Additional input files.


.SS signals-ignore
.IP "Type:" 6
\f[I]list\f[] of \f[I]strings\f[]

.IP "Example:" 4
["SIGTTOU", "SIGTTIN", "SIGTERM"]

.IP "Description:" 4
The list of signal names to ignore, i.e. set
\f[I]SIG_IGN\f[]
as signal handler for.


.SS subconfigs
.IP "Type:" 6
\f[I]list\f[] of \f[I]Path\f[]

.IP "Example:" 4
["~/cfg-twitter.json", "~/cfg-reddit.json"]

.IP "Description:" 4
Additional configuration files to load.


.SS warnings
.IP "Type:" 6
\f[I]string\f[]

.IP "Default:" 9
\f[I]"default"\f[]

.IP "Description:" 4
The \f[I]Warnings Filter action\f[]
used for (urllib3) warnings.


.SH API TOKENS & IDS
.SS extractor.deviantart.client-id & .client-secret
.IP "Type:" 6
\f[I]string\f[]

.IP "How To:" 4
.br
* login and visit DeviantArt's
\f[I]Applications & Keys\f[]
section
.br
* click "Register Application"
.br
* scroll to "OAuth2 Redirect URI Whitelist (Required)"
and enter "https://mikf.github.io/gallery-dl/oauth-redirect.html"
.br
* scroll to the bottom and agree to the API License Agreement.
Submission Policy, and Terms of Service.
.br
* click "Save"
.br
* copy \f[I]client_id\f[] and \f[I]client_secret\f[] of your new
application and put them in your configuration file
as \f[I]"client-id"\f[] and \f[I]"client-secret"\f[]
.br
* clear your \f[I]cache\f[] to delete any remaining
\f[I]access-token\f[] entries. (\f[I]gallery-dl --clear-cache deviantart\f[])
.br
* get a new \f[I]refresh-token\f[] for the
new \f[I]client-id\f[] (\f[I]gallery-dl oauth:deviantart\f[])


.SS extractor.flickr.api-key & .api-secret
.IP "Type:" 6
\f[I]string\f[]

.IP "How To:" 4
.br
* login and \f[I]Create an App\f[]
in Flickr's \f[I]App Garden\f[]
.br
* click "APPLY FOR A NON-COMMERCIAL KEY"
.br
* fill out the form with a random name and description
and click "SUBMIT"
.br
* copy \f[I]Key\f[] and \f[I]Secret\f[] and put them in your configuration file
as \f[I]"api-key"\f[] and \f[I]"api-secret"\f[]


.SS extractor.mangadex.client-id & .client-secret
.IP "Type:" 6
\f[I]string\f[]

.IP "How To:" 4
.br
* login and go to your \f[I]User Settings\f[]
.br
* open the "API Clients" section
.br
* click "\f[I]+ Create\f[]"
.br
* choose a name
.br
* click "\f[I]✔️ Create\f[]"
.br
* wait for approval / reload the page
.br
* copy the value after "AUTOAPPROVED ACTIVE" in the form "personal-client-..."
and put it in your configuration file as \f[I]"client-id"\f[]
.br
* click "\f[I]Get Secret\f[]", then "\f[I]Copy Secret\f[]",
and paste it into your configuration file as \f[I]"client-secret"\f[]


.SS extractor.reddit.client-id & .user-agent
.IP "Type:" 6
\f[I]string\f[]

.IP "How To:" 4
.br
* login and visit the \f[I]apps\f[]
section of your account's preferences
.br
* click the "are you a developer? create an app..." button
.br
* fill out the form:

.br
* choose a name
.br
* select "installed app"
.br
* set \f[I]http://localhost:6414/\f[] as "redirect uri"
.br
* solve the "I'm not a robot" reCAPTCHA if needed
.br
* click "create app"

.br
* copy the client id (third line, under your application's name and
"installed app") and put it in your configuration file
as \f[I]"client-id"\f[]
.br
* use "\f[I]Python:<application name>:v1.0 (by /u/<username>)\f[]" as
\f[I]user-agent\f[] and replace \f[I]<application name>\f[] and \f[I]<username>\f[]
accordingly (see Reddit's
\f[I]API access rules\f[])
.br
* clear your \f[I]cache\f[] to delete any remaining
\f[I]access-token\f[] entries. (\f[I]gallery-dl --clear-cache reddit\f[])
.br
* get a \f[I]refresh-token\f[] for the
new \f[I]client-id\f[] (\f[I]gallery-dl oauth:reddit\f[])


.SS extractor.smugmug.api-key & .api-secret
.IP "Type:" 6
\f[I]string\f[]

.IP "How To:" 4
.br
* login and \f[I]Apply for an API Key\f[]
.br
* use a random name and description,
set "Type" to "Application", "Platform" to "All",
and "Use" to "Non-Commercial"
.br
* fill out the two checkboxes at the bottom and click "Apply"
.br
* copy \f[I]API Key\f[] and \f[I]API Secret\f[]
and put them in your configuration file
as \f[I]"api-key"\f[] and \f[I]"api-secret"\f[]


.SS extractor.tumblr.api-key & .api-secret
.IP "Type:" 6
\f[I]string\f[]

.IP "How To:" 4
.br
* login and visit Tumblr's
\f[I]Applications\f[] section
.br
* click "Register application"
.br
* fill out the form: use a random name and description, set
https://example.org/ as "Application Website" and "Default
callback URL"
.br
* solve Google's "I'm not a robot" challenge and click "Register"
.br
* click "Show secret key" (below "OAuth Consumer Key")
.br
* copy your \f[I]OAuth Consumer Key\f[] and \f[I]Secret Key\f[]
and put them in your configuration file
as \f[I]"api-key"\f[] and \f[I]"api-secret"\f[]


.SH CUSTOM TYPES
.SS Date
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]integer\f[]

.IP "Example:" 4
.br
* "2019-01-01T00:00:00"
.br
* "2019" with "%Y" as \f[I]date-format\f[]
.br
* 1546297200

.IP "Description:" 4
A \f[I]Date\f[] value represents a specific point in time.

.br
* If given as \f[I]string\f[], it is parsed according to \f[I]date-format\f[].
.br
* If given as \f[I]integer\f[], it is interpreted as UTC timestamp.


.SS Duration
.IP "Type:" 6
.br
* \f[I]float\f[]
.br
* \f[I]list\f[] with 2 \f[I]floats\f[]
.br
* \f[I]string\f[]

.IP "Example:" 4
.br
* 2.85
.br
* [1.5, 3.0]
.br
* "2.85", "1.5-3.0"

.IP "Description:" 4
A \f[I]Duration\f[] represents a span of time in seconds.

.br
* If given as a single \f[I]float\f[], it will be used as that exact value.
.br
* If given as a \f[I]list\f[] with 2 floating-point numbers \f[I]a\f[] & \f[I]b\f[] ,
it will be randomly chosen with uniform distribution such that \f[I]a <= N <= b\f[].
(see \f[I]random.uniform()\f[])
.br
* If given as a \f[I]string\f[], it can either represent a single \f[I]float\f[]
value (\f[I]"2.85"\f[]) or a range  (\f[I]"1.5-3.0"\f[]).


.SS Path
.IP "Type:" 6
.br
* \f[I]string\f[]
.br
* \f[I]list\f[] of \f[I]strings\f[]

.IP "Example:" 4
.br
* "file.ext"
.br
* "~/path/to/file.ext"
.br
* "$HOME/path/to/file.ext"
.br
* ["$HOME", "path", "to", "file.ext"]

.IP "Description:" 4
A \f[I]Path\f[] is a \f[I]string\f[] representing the location of a file
or directory.

Simple \f[I]tilde expansion\f[]
and \f[I]environment variable expansion\f[]
is supported.

In Windows environments, backslashes (\f[I]"\\"\f[]) can, in addition to
forward slashes (\f[I]"/"\f[]), be used as path separators.
Because backslashes are JSON's escape character,
they themselves have to be escaped.
The path \f[I]C:\\path\\to\\file.ext\f[] has therefore to be written as
\f[I]"C:\\\\path\\\\to\\\\file.ext"\f[] if you want to use backslashes.


.SS Logging Configuration
.IP "Type:" 6
\f[I]object\f[]

.IP "Example:" 4
.. code:: json

{
"format"     : "{asctime} {name}: {message}",
"format-date": "%H:%M:%S",
"path"       : "~/log.txt",
"encoding"   : "ascii"
}

.. code:: json

{
"level" : "debug",
"format": {
"debug"  : "debug: {message}",
"info"   : "[{name}] {message}",
"warning": "Warning: {message}",
"error"  : "ERROR: {message}"
}
}


.IP "Description:" 4
Extended logging output configuration.

.br
* format
.br
* General format string for logging messages
or an \f[I]object\f[] with format strings for each loglevel.

In addition to the default
\f[I]LogRecord attributes\f[],
it is also possible to access the current
\f[I]extractor\f[],
\f[I]job\f[],
\f[I]path\f[],
and keywords objects and their attributes, for example
\f[I]"{extractor.url}"\f[], \f[I]"{path.filename}"\f[], \f[I]"{keywords.title}"\f[]
.br
* Default: \f[I]"[{name}][{levelname}] {message}"\f[]
.br
* format-date
.br
* Format string for \f[I]{asctime}\f[] fields in logging messages
(see \f[I]strftime() directives\f[])
.br
* Default: \f[I]"%Y-%m-%d %H:%M:%S"\f[]
.br
* level
.br
* Minimum logging message level
(one of \f[I]"debug"\f[], \f[I]"info"\f[], \f[I]"warning"\f[], \f[I]"error"\f[], \f[I]"exception"\f[])
.br
* Default: \f[I]"info"\f[]
.br
* path
.br
* \f[I]Path\f[] to the output file
.br
* mode
.br
* Mode in which the file is opened;
use \f[I]"w"\f[] to truncate or \f[I]"a"\f[] to append
(see \f[I]open()\f[])
.br
* Default: \f[I]"w"\f[]
.br
* encoding
.br
* File encoding
.br
* Default: \f[I]"utf-8"\f[]

Note: path, mode, and encoding are only applied when configuring
logging output to a file.


.SS Postprocessor Configuration
.IP "Type:" 6
\f[I]object\f[]

.IP "Example:" 4
.. code:: json

{ "name": "mtime" }

.. code:: json

{
"name"       : "zip",
"compression": "store",
"extension"  : "cbz",
"filter"     : "extension not in ('zip', 'rar')",
"whitelist"  : ["mangadex", "exhentai", "nhentai"]
}

.IP "Description:" 4
An \f[I]object\f[] containing a \f[I]"name"\f[] attribute specifying the
post-processor type, as well as any of its \f[I]options\f[].

It is possible to set a \f[I]"filter"\f[] expression similar to
\f[I]image-filter\f[] to only run a post-processor
conditionally.

It is also possible set a \f[I]"whitelist"\f[] or \f[I]"blacklist"\f[] to
only enable or disable a post-processor for the specified
extractor categories.

The available post-processor types are

\f[I]classify\f[]
Categorize files by filename extension
\f[I]compare\f[]
Compare versions of the same file and replace/enumerate them on mismatch
.br
(requires \f[I]downloader.*.part\f[] = \f[I]true\f[] and \f[I]extractor.*.skip\f[] = \f[I]false\f[])
.br
\f[I]directory\f[]
Reevaluate \f[I]directory\f[] format strings
\f[I]exec\f[]
Execute external commands
\f[I]hash\f[]
Compute file hash digests
\f[I]metadata\f[]
Write metadata to separate files
\f[I]mtime\f[]
Set file modification time according to its metadata
\f[I]python\f[]
Call Python functions
\f[I]rename\f[]
Rename previously downloaded files
\f[I]ugoira\f[]
Convert Pixiv Ugoira to WebM using \f[I]ffmpeg\f[]
\f[I]zip\f[]
Store files in a ZIP archive



.SH BUGS
https://github.com/mikf/gallery-dl/issues

.SH AUTHORS
Mike Fährmann <<EMAIL>>
.br
and https://github.com/mikf/gallery-dl/graphs/contributors

.SH "SEE ALSO"
.BR gallery-dl (1)
