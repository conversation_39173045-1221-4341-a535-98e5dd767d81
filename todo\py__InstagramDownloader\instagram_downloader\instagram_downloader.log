2025-07-11T10:36:04.715978+0200 - INFO - Download successful: https://www.instagram.com/reel/DG8OCzERtTx/?igsh=dWx6Y3kyZW9ocWQ=
2025-07-11T10:39:05.187447+0200 - ERROR - Download failed for https://www.instagram.com/reel/DG8OCzERtTx/?igsh=dWx6Y3kyZW9ocWQ=: 'ext'
Traceback (most recent call last):

  File "C:\Users\<USER>\Desktop\__SCRATCH__\2025.07.11-kl.08.19--replace_pip_with_uv\replace_pip_with_uv\todo\py__InstagramDownloader\instagram_downloader\src\main.py", line 311, in <module>
    main()
    └ <function main at 0x0000022BBC2DDB20>

  File "C:\Users\<USER>\Desktop\__SCRATCH__\2025.07.11-kl.08.19--replace_pip_with_uv\replace_pip_with_uv\todo\py__InstagramDownloader\instagram_downloader\src\main.py", line 297, in main
    results = download_posts(urls, output_dir, download_options)
              │              │     │           └ {'name': 'Video Only: Full Quality', 'download_videos': True, 'download_images': False, 'compress_json': True}
              │              │     └ WindowsPath('C:/Users/<USER>/Desktop/__SCRATCH__/2025.07.11-kl.08.19--replace_pip_with_uv/replace_pip_with_uv/todo/py__Instagram...
              │              └ ['https://www.instagram.com/reel/DG8OCzERtTx/?igsh=dWx6Y3kyZW9ocWQ=']
              └ <function download_posts at 0x0000022BBC2DD800>

  File "C:\Users\<USER>\Desktop\__SCRATCH__\2025.07.11-kl.08.19--replace_pip_with_uv\replace_pip_with_uv\todo\py__InstagramDownloader\instagram_downloader\src\main.py", line 202, in download_posts
    result = download_post(url, output_dir, options, progress, task_id)
             │             │    │           │        │         └ 1
             │             │    │           │        └ <rich.progress.Progress object at 0x0000022BBC2D8980>
             │             │    │           └ {'name': 'Video Only: Full Quality', 'download_videos': True, 'download_images': False, 'compress_json': True}
             │             │    └ WindowsPath('C:/Users/<USER>/Desktop/__SCRATCH__/2025.07.11-kl.08.19--replace_pip_with_uv/replace_pip_with_uv/todo/py__Instagram...
             │             └ 'https://www.instagram.com/reel/DG8OCzERtTx/?igsh=dWx6Y3kyZW9ocWQ='
             └ <function download_post at 0x0000022BBC2DD760>

> File "C:\Users\<USER>\Desktop\__SCRATCH__\2025.07.11-kl.08.19--replace_pip_with_uv\replace_pip_with_uv\todo\py__InstagramDownloader\instagram_downloader\src\main.py", line 168, in download_post
    loader.download_post(post, target=str(output_dir))
    │      │             │                └ WindowsPath('C:/Users/<USER>/Desktop/__SCRATCH__/2025.07.11-kl.08.19--replace_pip_with_uv/replace_pip_with_uv/todo/py__Instagram...
    │      │             └ <Post DG8OCzERtTx>
    │      └ <function Instaloader.download_post at 0x0000022BBBE7E480>
    └ <instaloader.instaloader.Instaloader object at 0x0000022BBC2EA850>

  File "C:\Users\<USER>\Desktop\__SCRATCH__\2025.07.11-kl.08.19--replace_pip_with_uv\replace_pip_with_uv\todo\py__InstagramDownloader\instagram_downloader\.venv\Lib\site-packages\instaloader\instaloader.py", line 719, in download_post
    filename_template = os.path.join(dirname, self.format_filename(post, target=target))
                        │  │    │    │        │    │               │            └ 'C:\\Users\\<USER>\\Desktop\\__SCRATCH__\\2025.07.11-kl.08.19--replace_pip_with_uv\\replace_pip_with_uv\\todo\\py__InstagramDown...
                        │  │    │    │        │    │               └ <Post DG8OCzERtTx>
                        │  │    │    │        │    └ <function Instaloader.format_filename at 0x0000022BBBE7E3E0>
                        │  │    │    │        └ <instaloader.instaloader.Instaloader object at 0x0000022BBC2EA850>
                        │  │    │    └ 'C:\\Users\\<USER>\\Desktop\\__SCRATCH__\\2025.07.11-kl.08.19--replace_pip_with_uv\\replace_pip_with_uv\\todo\\py__InstagramDown...
                        │  │    └ <function join at 0x0000022BB8DEFEC0>
                        │  └ <module 'ntpath' (frozen)>
                        └ <module 'os' (frozen)>
  File "C:\Users\<USER>\Desktop\__SCRATCH__\2025.07.11-kl.08.19--replace_pip_with_uv\replace_pip_with_uv\todo\py__InstagramDownloader\instagram_downloader\.venv\Lib\site-packages\instaloader\instaloader.py", line 686, in format_filename
    return _PostPathFormatter(item, self.sanitize_paths).format(self.filename_pattern, target=target)
           │                  │     │    │                      │    │                        └ 'C:\\Users\\<USER>\\Desktop\\__SCRATCH__\\2025.07.11-kl.08.19--replace_pip_with_uv\\replace_pip_with_uv\\todo\\py__InstagramDown...
           │                  │     │    │                      │    └ 'elkeyzandstra-Still one of my favorite videos to date.{ext}'
           │                  │     │    │                      └ <instaloader.instaloader.Instaloader object at 0x0000022BBC2EA850>
           │                  │     │    └ False
           │                  │     └ <instaloader.instaloader.Instaloader object at 0x0000022BBC2EA850>
           │                  └ <Post DG8OCzERtTx>
           └ <class 'instaloader.instaloader._PostPathFormatter'>
  File "C:\Python313\Lib\string.py", line 190, in format
    return self.vformat(format_string, args, kwargs)
           │    │       │              │     └ {'target': 'C:\\Users\\<USER>\\Desktop\\__SCRATCH__\\2025.07.11-kl.08.19--replace_pip_with_uv\\replace_pip_with_uv\\todo\\py__In...
           │    │       │              └ ()
           │    │       └ 'elkeyzandstra-Still one of my favorite videos to date.{ext}'
           │    └ <function Formatter.vformat at 0x0000022BBAFFBA60>
           └ <instaloader.instaloader._PostPathFormatter object at 0x0000022BBC2EA990>
  File "C:\Python313\Lib\string.py", line 194, in vformat
    result, _ = self._vformat(format_string, args, kwargs, used_args, 2)
                │    │        │              │     │       └ set()
                │    │        │              │     └ {'target': 'C:\\Users\\<USER>\\Desktop\\__SCRATCH__\\2025.07.11-kl.08.19--replace_pip_with_uv\\replace_pip_with_uv\\todo\\py__In...
                │    │        │              └ ()
                │    │        └ 'elkeyzandstra-Still one of my favorite videos to date.{ext}'
                │    └ <function Formatter._vformat at 0x0000022BBAFFBB00>
                └ <instaloader.instaloader._PostPathFormatter object at 0x0000022BBC2EA990>
  File "C:\Python313\Lib\string.py", line 234, in _vformat
    obj, arg_used = self.get_field(field_name, args, kwargs)
                    │    │         │           │     └ {'target': 'C:\\Users\\<USER>\\Desktop\\__SCRATCH__\\2025.07.11-kl.08.19--replace_pip_with_uv\\replace_pip_with_uv\\todo\\py__In...
                    │    │         │           └ ()
                    │    │         └ 'ext'
                    │    └ <function Formatter.get_field at 0x0000022BBAFFBEC0>
                    └ <instaloader.instaloader._PostPathFormatter object at 0x0000022BBC2EA990>
  File "C:\Python313\Lib\string.py", line 299, in get_field
    obj = self.get_value(first, args, kwargs)
          │    │         │      │     └ {'target': 'C:\\Users\\<USER>\\Desktop\\__SCRATCH__\\2025.07.11-kl.08.19--replace_pip_with_uv\\replace_pip_with_uv\\todo\\py__In...
          │    │         │      └ ()
          │    │         └ 'ext'
          │    └ <function _PostPathFormatter.get_value at 0x0000022BBBE7CFE0>
          └ <instaloader.instaloader._PostPathFormatter object at 0x0000022BBC2EA990>
  File "C:\Users\<USER>\Desktop\__SCRATCH__\2025.07.11-kl.08.19--replace_pip_with_uv\replace_pip_with_uv\todo\py__InstagramDownloader\instagram_downloader\.venv\Lib\site-packages\instaloader\instaloader.py", line 149, in get_value
    ret = super().get_value(key, args, kwargs)
                            │    │     └ {'target': 'C:\\Users\\<USER>\\Desktop\\__SCRATCH__\\2025.07.11-kl.08.19--replace_pip_with_uv\\replace_pip_with_uv\\todo\\py__In...
                            │    └ ()
                            └ 'ext'
  File "C:\Users\<USER>\Desktop\__SCRATCH__\2025.07.11-kl.08.19--replace_pip_with_uv\replace_pip_with_uv\todo\py__InstagramDownloader\instagram_downloader\.venv\Lib\site-packages\instaloader\instaloader.py", line 126, in get_value
    return super().get_value(key, args, kwargs)
                             │    │     └ {'target': 'C:\\Users\\<USER>\\Desktop\\__SCRATCH__\\2025.07.11-kl.08.19--replace_pip_with_uv\\replace_pip_with_uv\\todo\\py__In...
                             │    └ ()
                             └ 'ext'
  File "C:\Python313\Lib\string.py", line 256, in get_value
    return kwargs[key]
           │      └ 'ext'
           └ {'target': 'C:\\Users\\<USER>\\Desktop\\__SCRATCH__\\2025.07.11-kl.08.19--replace_pip_with_uv\\replace_pip_with_uv\\todo\\py__In...

KeyError: 'ext'
2025-07-11T10:39:32.039403+0200 - ERROR - Download failed for https://www.instagram.com/reel/DG8OCzERtTx/?igsh=dWx6Y3kyZW9ocWQ=: 'ext'
Traceback (most recent call last):

  File "C:\Users\<USER>\Desktop\__SCRATCH__\2025.07.11-kl.08.19--replace_pip_with_uv\replace_pip_with_uv\todo\py__InstagramDownloader\instagram_downloader\src\main.py", line 311, in <module>
    main()
    └ <function main at 0x0000027237BBDB20>

  File "C:\Users\<USER>\Desktop\__SCRATCH__\2025.07.11-kl.08.19--replace_pip_with_uv\replace_pip_with_uv\todo\py__InstagramDownloader\instagram_downloader\src\main.py", line 297, in main
    results = download_posts(urls, output_dir, download_options)
              │              │     │           └ {'name': 'Video Only: Full Quality', 'download_videos': True, 'download_images': False, 'compress_json': True}
              │              │     └ WindowsPath('C:/Users/<USER>/Desktop/__SCRATCH__/2025.07.11-kl.08.19--replace_pip_with_uv/replace_pip_with_uv/todo/py__Instagram...
              │              └ ['https://www.instagram.com/reel/DG8OCzERtTx/?igsh=dWx6Y3kyZW9ocWQ=']
              └ <function download_posts at 0x0000027237BBD800>

  File "C:\Users\<USER>\Desktop\__SCRATCH__\2025.07.11-kl.08.19--replace_pip_with_uv\replace_pip_with_uv\todo\py__InstagramDownloader\instagram_downloader\src\main.py", line 202, in download_posts
    result = download_post(url, output_dir, options, progress, task_id)
             │             │    │           │        │         └ 1
             │             │    │           │        └ <rich.progress.Progress object at 0x0000027237BB8980>
             │             │    │           └ {'name': 'Video Only: Full Quality', 'download_videos': True, 'download_images': False, 'compress_json': True}
             │             │    └ WindowsPath('C:/Users/<USER>/Desktop/__SCRATCH__/2025.07.11-kl.08.19--replace_pip_with_uv/replace_pip_with_uv/todo/py__Instagram...
             │             └ 'https://www.instagram.com/reel/DG8OCzERtTx/?igsh=dWx6Y3kyZW9ocWQ='
             └ <function download_post at 0x0000027237BBD760>

> File "C:\Users\<USER>\Desktop\__SCRATCH__\2025.07.11-kl.08.19--replace_pip_with_uv\replace_pip_with_uv\todo\py__InstagramDownloader\instagram_downloader\src\main.py", line 168, in download_post
    loader.download_post(post, target=str(output_dir))
    │      │             │                └ WindowsPath('C:/Users/<USER>/Desktop/__SCRATCH__/2025.07.11-kl.08.19--replace_pip_with_uv/replace_pip_with_uv/todo/py__Instagram...
    │      │             └ <Post DG8OCzERtTx>
    │      └ <function Instaloader.download_post at 0x000002723771E480>
    └ <instaloader.instaloader.Instaloader object at 0x0000027237BCE850>

  File "C:\Users\<USER>\Desktop\__SCRATCH__\2025.07.11-kl.08.19--replace_pip_with_uv\replace_pip_with_uv\todo\py__InstagramDownloader\instagram_downloader\.venv\Lib\site-packages\instaloader\instaloader.py", line 719, in download_post
    filename_template = os.path.join(dirname, self.format_filename(post, target=target))
                        │  │    │    │        │    │               │            └ 'C:\\Users\\<USER>\\Desktop\\__SCRATCH__\\2025.07.11-kl.08.19--replace_pip_with_uv\\replace_pip_with_uv\\todo\\py__InstagramDown...
                        │  │    │    │        │    │               └ <Post DG8OCzERtTx>
                        │  │    │    │        │    └ <function Instaloader.format_filename at 0x000002723771E3E0>
                        │  │    │    │        └ <instaloader.instaloader.Instaloader object at 0x0000027237BCE850>
                        │  │    │    └ 'C:\\Users\\<USER>\\Desktop\\__SCRATCH__\\2025.07.11-kl.08.19--replace_pip_with_uv\\replace_pip_with_uv\\todo\\py__InstagramDown...
                        │  │    └ <function join at 0x00000272346BFEC0>
                        │  └ <module 'ntpath' (frozen)>
                        └ <module 'os' (frozen)>
  File "C:\Users\<USER>\Desktop\__SCRATCH__\2025.07.11-kl.08.19--replace_pip_with_uv\replace_pip_with_uv\todo\py__InstagramDownloader\instagram_downloader\.venv\Lib\site-packages\instaloader\instaloader.py", line 686, in format_filename
    return _PostPathFormatter(item, self.sanitize_paths).format(self.filename_pattern, target=target)
           │                  │     │    │                      │    │                        └ 'C:\\Users\\<USER>\\Desktop\\__SCRATCH__\\2025.07.11-kl.08.19--replace_pip_with_uv\\replace_pip_with_uv\\todo\\py__InstagramDown...
           │                  │     │    │                      │    └ 'elkeyzandstra-Still one of my favorite videos to date.{ext}'
           │                  │     │    │                      └ <instaloader.instaloader.Instaloader object at 0x0000027237BCE850>
           │                  │     │    └ False
           │                  │     └ <instaloader.instaloader.Instaloader object at 0x0000027237BCE850>
           │                  └ <Post DG8OCzERtTx>
           └ <class 'instaloader.instaloader._PostPathFormatter'>
  File "C:\Python313\Lib\string.py", line 190, in format
    return self.vformat(format_string, args, kwargs)
           │    │       │              │     └ {'target': 'C:\\Users\\<USER>\\Desktop\\__SCRATCH__\\2025.07.11-kl.08.19--replace_pip_with_uv\\replace_pip_with_uv\\todo\\py__In...
           │    │       │              └ ()
           │    │       └ 'elkeyzandstra-Still one of my favorite videos to date.{ext}'
           │    └ <function Formatter.vformat at 0x000002723686BA60>
           └ <instaloader.instaloader._PostPathFormatter object at 0x0000027237BCE0D0>
  File "C:\Python313\Lib\string.py", line 194, in vformat
    result, _ = self._vformat(format_string, args, kwargs, used_args, 2)
                │    │        │              │     │       └ set()
                │    │        │              │     └ {'target': 'C:\\Users\\<USER>\\Desktop\\__SCRATCH__\\2025.07.11-kl.08.19--replace_pip_with_uv\\replace_pip_with_uv\\todo\\py__In...
                │    │        │              └ ()
                │    │        └ 'elkeyzandstra-Still one of my favorite videos to date.{ext}'
                │    └ <function Formatter._vformat at 0x000002723686BB00>
                └ <instaloader.instaloader._PostPathFormatter object at 0x0000027237BCE0D0>
  File "C:\Python313\Lib\string.py", line 234, in _vformat
    obj, arg_used = self.get_field(field_name, args, kwargs)
                    │    │         │           │     └ {'target': 'C:\\Users\\<USER>\\Desktop\\__SCRATCH__\\2025.07.11-kl.08.19--replace_pip_with_uv\\replace_pip_with_uv\\todo\\py__In...
                    │    │         │           └ ()
                    │    │         └ 'ext'
                    │    └ <function Formatter.get_field at 0x000002723686BEC0>
                    └ <instaloader.instaloader._PostPathFormatter object at 0x0000027237BCE0D0>
  File "C:\Python313\Lib\string.py", line 299, in get_field
    obj = self.get_value(first, args, kwargs)
          │    │         │      │     └ {'target': 'C:\\Users\\<USER>\\Desktop\\__SCRATCH__\\2025.07.11-kl.08.19--replace_pip_with_uv\\replace_pip_with_uv\\todo\\py__In...
          │    │         │      └ ()
          │    │         └ 'ext'
          │    └ <function _PostPathFormatter.get_value at 0x000002723771CFE0>
          └ <instaloader.instaloader._PostPathFormatter object at 0x0000027237BCE0D0>
  File "C:\Users\<USER>\Desktop\__SCRATCH__\2025.07.11-kl.08.19--replace_pip_with_uv\replace_pip_with_uv\todo\py__InstagramDownloader\instagram_downloader\.venv\Lib\site-packages\instaloader\instaloader.py", line 149, in get_value
    ret = super().get_value(key, args, kwargs)
                            │    │     └ {'target': 'C:\\Users\\<USER>\\Desktop\\__SCRATCH__\\2025.07.11-kl.08.19--replace_pip_with_uv\\replace_pip_with_uv\\todo\\py__In...
                            │    └ ()
                            └ 'ext'
  File "C:\Users\<USER>\Desktop\__SCRATCH__\2025.07.11-kl.08.19--replace_pip_with_uv\replace_pip_with_uv\todo\py__InstagramDownloader\instagram_downloader\.venv\Lib\site-packages\instaloader\instaloader.py", line 126, in get_value
    return super().get_value(key, args, kwargs)
                             │    │     └ {'target': 'C:\\Users\\<USER>\\Desktop\\__SCRATCH__\\2025.07.11-kl.08.19--replace_pip_with_uv\\replace_pip_with_uv\\todo\\py__In...
                             │    └ ()
                             └ 'ext'
  File "C:\Python313\Lib\string.py", line 256, in get_value
    return kwargs[key]
           │      └ 'ext'
           └ {'target': 'C:\\Users\\<USER>\\Desktop\\__SCRATCH__\\2025.07.11-kl.08.19--replace_pip_with_uv\\replace_pip_with_uv\\todo\\py__In...

KeyError: 'ext'
2025-07-11T10:40:39.211797+0200 - INFO - Using filename pattern: elkeyzandstra-Still one of my favorite videos to date
2025-07-11T10:40:39.553194+0200 - INFO - Download successful: https://www.instagram.com/reel/DG8OCzERtTx/?igsh=dWx6Y3kyZW9ocWQ=
2025-07-11T10:42:05.545612+0200 - INFO - Using filename pattern: elkeyzandstra-Still one of my favorite videos to date
2025-07-11T10:42:05.880970+0200 - INFO - Download successful: https://www.instagram.com/reel/DG8OCzERtTx/?igsh=dWx6Y3kyZW9ocWQ=
