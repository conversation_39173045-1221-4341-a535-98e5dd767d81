:: =============================================================================
:: cmd: initialize
:: =============================================================================
@ECHO OFF
SETLOCAL ENABLEEXTENSIONS ENABLEDELAYEDEXPANSION
IF EXIST "%~1" (CD /D "%~1") ELSE (CD /D "%~dp0")
SET "__init_path__=%CD%"
SET "__base_name__=%~n0"
SET "__venv_name__=venv"


:: =============================================================================
:: uv: check and execute
:: =============================================================================
WHERE uv >nul 2>&1
IF ERRORLEVEL 1 (
    ECHO uv is not installed, falling back to traditional venv...
    GOTO LocateVenv
) ELSE (
    ECHO Using uv to run application...
    GOTO UvExecute
)

:UvExecute
    IF NOT EXIST "pyproject.toml" (
        ECHO pyproject.toml not found, falling back to traditional venv...
        GOTO LocateVenv
    )
    "uv" run python %__base_name__%.py --prompt
    GOTO ReExecute

:: =============================================================================
:: venv: locate (fallback)
:: =============================================================================
SET "__venv_identifier__=%__venv_name__%\Scripts\python.exe"
:LocateVenv
    IF EXIST "%CD%\%__venv_identifier__%" (GOTO ActivateVenv)
    SET "tmp=%CD%" & CD .. & IF "%CD%"=="%tmp%" (
        ECHO Not found: %__venv_identifier__%
        ECHO make sure you've initialized the environment.
        CD /D "%__init_path__%"
        PAUSE>NUL & EXIT /B
    )
GOTO LocateVenv

:: =============================================================================
:: venv: activate (fallback)
:: =============================================================================
:ActivateVenv
    SET "__venv_stem__=%CD%"
    CD /D "%__init_path__%"
    CALL "%__venv_stem__%\%__venv_name__%\Scripts\activate"
    ECHO Using traditional venv...
    GOTO ExecuteCommand

:: =============================================================================
:: file: execute (fallback)
:: =============================================================================
:ExecuteCommand
    python %__base_name__%.py --prompt


:: =============================================================================
:: cmd: re-execute
:: =============================================================================
:ReExecute
ECHO. & ECHO Press a key to re-execute script ... & PAUSE > NUL & CLS
WHERE uv >nul 2>&1
IF ERRORLEVEL 1 (GOTO :ActivateVenv) ELSE (GOTO :UvExecute)
