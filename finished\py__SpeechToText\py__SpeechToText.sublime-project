{"folders": [{"path": ".", "folder_exclude_patterns": ["__pycache__", ".venv", "output", "logs"], "file_exclude_patterns": ["*.pyc", "*.log"]}], "settings": {"python_interpreter": "./speech_to_text/.venv/Scripts/python.exe", "rulers": [88], "tab_size": 4, "translate_tabs_to_spaces": true, "trim_trailing_white_space_on_save": true}, "build_systems": [{"name": "Speech-to-Text", "cmd": ["uv", "run", "python", "src/main.py"], "working_dir": "${project_path}/speech_to_text", "shell": true}]}