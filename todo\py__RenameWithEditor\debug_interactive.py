#!/usr/bin/env python3
"""Debug script to test the interactive functionality step by step."""

import sys
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent / "bookmark_folderizer" / "src"))

def test_file_finding():
    """Test if files are being found correctly."""
    try:
        from main import find_bookmark_files
        
        working_dir = Path.cwd()
        print(f"🔍 Looking for bookmark files in: {working_dir}")
        
        bookmark_files = find_bookmark_files(working_dir)
        print(f"📁 Found {len(bookmark_files)} bookmark files:")
        
        for i, file_path in enumerate(bookmark_files, 1):
            if file_path.is_file():
                size = file_path.stat().st_size
                file_type = file_path.suffix.upper()
                print(f"  {i}. {file_path.name} ({file_type}, {size} bytes)")
            else:
                item_count = len(list(file_path.iterdir())) if file_path.exists() else 0
                print(f"  {i}. {file_path.name}/ (Directory, {item_count} items)")
        
        # Also check 'in' directory
        in_dir = working_dir / "in"
        if in_dir.exists():
            print(f"\n🔍 Also checking 'in' directory: {in_dir}")
            in_files = find_bookmark_files(in_dir)
            print(f"📁 Found {len(in_files)} additional files in 'in' directory")
            
        return len(bookmark_files) > 0
        
    except Exception as e:
        print(f"❌ Error testing file finding: {e}")
        return False

def test_conversion_modes():
    """Test conversion mode detection."""
    try:
        from main import get_conversion_mode_interactive
        
        # Test with different file types
        test_files = [
            Path("test.html"),
            Path("test.json"), 
            Path("test.URLS")
        ]
        
        print("\n🧪 Testing conversion mode detection:")
        
        for test_file in test_files:
            print(f"\n📄 Testing: {test_file}")
            print(f"   is_file(): {test_file.is_file()}")
            print(f"   is_dir(): {test_file.is_dir()}")
            print(f"   exists(): {test_file.exists()}")
            print(f"   suffix: {test_file.suffix}")
            print(f"   name.endswith('.URLS'): {test_file.name.endswith('.URLS')}")
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing conversion modes: {e}")
        return False

if __name__ == "__main__":
    print("🚀 BookmarkFolderizer Interactive Debug Test")
    print("=" * 50)
    
    success1 = test_file_finding()
    success2 = test_conversion_modes()
    
    overall_success = success1 and success2
    print(f"\n{'✅ All tests passed!' if overall_success else '❌ Some tests failed!'}")
    
    if not success1:
        print("\n💡 Tip: Make sure you have some .html, .json, or .URLS files in the current directory")
        print("   or in an 'in' subdirectory for the interactive CLI to work properly.")
