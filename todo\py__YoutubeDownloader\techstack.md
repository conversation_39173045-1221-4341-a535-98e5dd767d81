# Technology Stack

## Core Dependencies
- **Python 3.9+** - Runtime environment (required by yt-dlp)
- **yt-dlp** - YouTube video/audio downloading
- **rich** - Terminal UI and progress visualization
- **loguru** - Structured logging
- **pyperclip** - Clipboard integration

## Development Tools
- **uv** - Modern Python package management
- **Windows Batch** - Automation scripts

## Architecture
- **Single-file application** - All functionality in main.py
- **CLI interface** - Rich-based interactive prompts
- **Concurrent downloads** - ThreadPoolExecutor for parallel processing
- **Progress tracking** - Real-time download progress with Rich
- **Logging** - Structured logging with cleanup functionality
