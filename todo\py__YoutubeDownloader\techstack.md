# Technology Stack

## Core Dependencies
- **Python 3.x** - Runtime environment
- **yt-dlp** - YouTube video/audio downloading
- **rich** - Terminal UI and progress visualization
- **loguru** - Structured logging
- **pyperclip** - Clipboard integration

## Development Tools
- **pip** - Package management (to be replaced with uv)
- **venv** - Virtual environment management
- **Windows Batch** - Automation scripts

## Architecture
- **Single-file application** - All functionality in main.py
- **CLI interface** - Rich-based interactive prompts
- **Concurrent downloads** - ThreadPoolExecutor for parallel processing
- **Progress tracking** - Real-time download progress with Rich
- **Logging** - Structured logging with cleanup functionality
