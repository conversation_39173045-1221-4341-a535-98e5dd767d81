:: =============================================================================
:: cmd: initialize
:: =============================================================================
@ECHO OFF
SETLOCAL ENABLEEXTENSIONS ENABLEDELAYEDEXPANSION
IF EXIST "%~1" (CD /D "%~1") ELSE (CD /D "%~dp0")
SET "__init_path__=%CD%"
SET "__base_name__=%~n0"
SET "__venv_name__=venv"


:: =============================================================================
:: venv: locate
:: =============================================================================
SET "__venv_identifier__=%__venv_name__%\Scripts\python.exe"
:LocateVenv
    IF EXIST "%CD%\%__venv_identifier__%" (GOTO ActivateVenv)
    SET "tmp=%CD%" & CD .. & IF "%CD%"=="%tmp%" (
        ECHO Not found: %__venv_identifier__%
        ECHO make sure you've initialized the venv.
        CD /D "%__init_path__%"
        PAUSE>NUL & EXIT /B
    )
GOTO LocateVenv


:: =============================================================================
:: venv: activate
:: =============================================================================
:ActivateVenv
    SET "__venv_stem__=%CD%"
    CD /D "%__init_path__%"
    CALL "%__venv_stem__%\%__venv_name__%\Scripts\activate"
    ::
    ECHO __init_path__: %__init_path__%
    ECHO __venv_stem__: %__venv_stem__%
    ECHO.
    GOTO ExecuteCommand


:: =============================================================================
:: packages: upgrade Pinterest downloader dependencies
:: =============================================================================
:ExecuteCommand
    ECHO Current installed packages:
    "pip" list & ECHO.
    
    ECHO ========================================
    ECHO Upgrading Pinterest Downloader packages
    ECHO ========================================
    ECHO.
    
    ECHO [1/5] Upgrading gallery-dl (core Pinterest downloader)...
    python -m pip install --upgrade gallery-dl
    ECHO.
    
    ECHO [2/5] Upgrading rich (UI components)...
    python -m pip install --upgrade rich
    ECHO.
    
    ECHO [3/5] Upgrading loguru (logging)...
    python -m pip install --upgrade loguru
    ECHO.
    
    ECHO [4/5] Upgrading pyperclip (clipboard integration)...
    python -m pip install --upgrade pyperclip
    ECHO.
    
    ECHO [5/5] Upgrading pip itself...
    python -m pip install --upgrade pip
    ECHO.
    
    ECHO ========================================
    ECHO Upgrade completed!
    ECHO ========================================
    ECHO.
    
    ECHO Updated package versions:
    "pip" list | findstr /i "gallery-dl rich loguru pyperclip"
    ECHO.


:: =============================================================================
:: requirements.txt: update
:: =============================================================================
SET "requirements_txt=%__init_path__%\requirements.txt"
ECHO Updating requirements.txt with new versions...
IF NOT ERRORLEVEL 1 ("pip" freeze > "%requirements_txt%") ELSE (
    ECHO Failed to write %requirements_txt%
)
ECHO Requirements updated: %requirements_txt%
ECHO.


:: =============================================================================
:: test: verify installation
:: =============================================================================
ECHO ========================================
ECHO Testing Pinterest downloader components
ECHO ========================================
ECHO.

ECHO Testing gallery-dl...
python -c "import gallery_dl; print(f'gallery-dl version: {gallery_dl.__version__}')" 2>NUL && (
    ECHO ✓ gallery-dl working
) || (
    ECHO ✗ gallery-dl has issues
)

ECHO Testing rich...
python -c "import rich; print('✓ rich working')" 2>NUL || ECHO ✗ rich has issues

ECHO Testing loguru...
python -c "import loguru; print('✓ loguru working')" 2>NUL || ECHO ✗ loguru has issues

ECHO Testing pyperclip...
python -c "import pyperclip; print('✓ pyperclip working')" 2>NUL || ECHO ✗ pyperclip has issues

ECHO.
ECHO Testing Pinterest downloader main script...
python src\main.py --help >NUL 2>&1 && (
    ECHO ✓ Pinterest downloader main script working
) || (
    ECHO ✗ Pinterest downloader main script has issues
)

ECHO.
ECHO ========================================
ECHO Upgrade and testing completed!
ECHO ========================================
ECHO.
ECHO You can now use the updated Pinterest downloader:
ECHO   python src\main.py --prompt
ECHO   or
ECHO   .\main.bat
ECHO.


:: =============================================================================
:: cmd: re-execute
:: =============================================================================
ECHO. & ECHO Press a key to re-execute script ... & PAUSE > NUL & CLS
GOTO :ActivateVenv
