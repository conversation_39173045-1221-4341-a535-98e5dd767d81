:: =============================================================================
:: UV Environment Initialization Script
:: =============================================================================
@ECHO OFF
SETLOCAL ENABLEEXTENSIONS ENABLEDELAYEDEXPANSION
IF EXIST "%~1" (CD /D "%~1") ELSE (CD /D "%~dp0")
SET "__init_path__=%CD%"
SET "__base_name__=%~n0"

:: =============================================================================
:: UV: Add to PATH if not already present
:: =============================================================================
SET "UV_PATH=C:\Users\<USER>\.local\bin"
ECHO %PATH% | FIND /I "%UV_PATH%" >NUL
IF ERRORLEVEL 1 (
    SET "PATH=%UV_PATH%;%PATH%"
    ECHO Added UV to PATH: %UV_PATH%
)

:: =============================================================================
:: UV: Verify installation
:: =============================================================================
uv --version >NUL 2>&1
IF ERRORLEVEL 1 (
    ECHO ERROR: UV is not installed or not in PATH
    ECHO Please install UV first: https://docs.astral.sh/uv/getting-started/installation/
    PAUSE
    EXIT /B 1
)

:: =============================================================================
:: UV: Initialize project and install dependencies
:: =============================================================================
ECHO Initializing UV project...
IF NOT EXIST "pyproject.toml" (
    ECHO ERROR: pyproject.toml not found
    ECHO Please ensure pyproject.toml exists in the project directory
    PAUSE
    EXIT /B 1
)

:: Install dependencies using UV
ECHO Installing dependencies with UV...
uv sync
IF ERRORLEVEL 1 (
    ECHO ERROR: Failed to install dependencies
    PAUSE
    EXIT /B 1
)

:: =============================================================================
:: UV: Activate virtual environment
:: =============================================================================
ECHO Activating UV virtual environment...
CALL .venv\Scripts\activate.bat
IF ERRORLEVEL 1 (
    ECHO ERROR: Failed to activate virtual environment
    PAUSE
    EXIT /B 1
)

ECHO.
ECHO UV environment initialized successfully!
ECHO Virtual environment activated.
ECHO.
ECHO Available commands:
ECHO   uv add [package]     - Add a new dependency
ECHO   uv remove [package]  - Remove a dependency
ECHO   uv sync              - Sync dependencies
ECHO   uv run [command]     - Run command in environment
ECHO.

:: =============================================================================
:: Keep terminal open
:: =============================================================================
ECHO Press any key to continue...
PAUSE > NUL
