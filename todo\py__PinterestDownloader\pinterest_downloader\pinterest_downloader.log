2025-07-11T11:34:15.826731+0200 - ERROR - Download failed for https://no.pinterest.com/pin/4925880836107375/: Expecting value: line 1 column 2 (char 1)
Traceback (most recent call last):

  File "C:\Users\<USER>\Desktop\__SCRATCH__\2025.07.11-kl.08.19--replace_pip_with_uv\replace_pip_with_uv\todo\py__PinterestDownloader\pinterest_downloader\src\main.py", line 478, in <module>
    main()
    └ <function main at 0x000001F87F5C5800>

  File "C:\Users\<USER>\Desktop\__SCRATCH__\2025.07.11-kl.08.19--replace_pip_with_uv\replace_pip_with_uv\todo\py__PinterestDownloader\pinterest_downloader\src\main.py", line 464, in main
    results = download_contents(urls, output_dir, download_options)
              │                 │     │           └ {}
              │                 │     └ WindowsPath('C:/Users/<USER>/Desktop/__SCRATCH__/2025.07.11-kl.08.19--replace_pip_with_uv/replace_pip_with_uv/todo/py__Pinterest...
              │                 └ ['https://no.pinterest.com/pin/4925880836107375/']
              └ <function download_contents at 0x000001F87F5C5620>

  File "C:\Users\<USER>\Desktop\__SCRATCH__\2025.07.11-kl.08.19--replace_pip_with_uv\replace_pip_with_uv\todo\py__PinterestDownloader\pinterest_downloader\src\main.py", line 403, in download_contents
    result = download_pinterest_content(url, output_dir, options, progress, task_id)
             │                          │    │           │        │         └ 1
             │                          │    │           │        └ <rich.progress.Progress object at 0x000001F87ED73230>
             │                          │    │           └ {}
             │                          │    └ WindowsPath('C:/Users/<USER>/Desktop/__SCRATCH__/2025.07.11-kl.08.19--replace_pip_with_uv/replace_pip_with_uv/todo/py__Pinterest...
             │                          └ 'https://no.pinterest.com/pin/4925880836107375/'
             └ <function download_pinterest_content at 0x000001F87F5C5580>

  File "C:\Users\<USER>\Desktop\__SCRATCH__\2025.07.11-kl.08.19--replace_pip_with_uv\replace_pip_with_uv\todo\py__PinterestDownloader\pinterest_downloader\src\main.py", line 385, in download_pinterest_content
    return extract_metadata_and_download(url, output_dir, options, progress, task_id)
           │                             │    │           │        │         └ 1
           │                             │    │           │        └ <rich.progress.Progress object at 0x000001F87ED73230>
           │                             │    │           └ {}
           │                             │    └ WindowsPath('C:/Users/<USER>/Desktop/__SCRATCH__/2025.07.11-kl.08.19--replace_pip_with_uv/replace_pip_with_uv/todo/py__Pinterest...
           │                             └ 'https://no.pinterest.com/pin/4925880836107375/'
           └ <function extract_metadata_and_download at 0x000001F87F5C54E0>

> File "C:\Users\<USER>\Desktop\__SCRATCH__\2025.07.11-kl.08.19--replace_pip_with_uv\replace_pip_with_uv\todo\py__PinterestDownloader\pinterest_downloader\src\main.py", line 269, in extract_metadata_and_download
    metadata = json.loads(metadata_lines[0])
               │    │     └ ['[', '  [', '    2,', '    {', '      "access": [],', '      "affiliate_link": null,', '      "aggregated_pin_data": {', '  ...
               │    └ <function loads at 0x000001F87ED4BBA0>
               └ <module 'json' from 'C:\\Python313\\Lib\\json\\__init__.py'>

  File "C:\Python313\Lib\json\__init__.py", line 346, in loads
    return _default_decoder.decode(s)
           │                │      └ '['
           │                └ <function JSONDecoder.decode at 0x000001F87ED4B420>
           └ <json.decoder.JSONDecoder object at 0x000001F87ED701A0>
  File "C:\Python313\Lib\json\decoder.py", line 345, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
               │    │          │      │  └ '['
               │    │          │      └ <built-in method match of re.Pattern object at 0x000001F87ED6C2B0>
               │    │          └ '['
               │    └ <function JSONDecoder.raw_decode at 0x000001F87ED4B4C0>
               └ <json.decoder.JSONDecoder object at 0x000001F87ED701A0>
  File "C:\Python313\Lib\json\decoder.py", line 363, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
          │                                  └ '['
          └ <class 'json.decoder.JSONDecodeError'>

json.decoder.JSONDecodeError: Expecting value: line 1 column 2 (char 1)
