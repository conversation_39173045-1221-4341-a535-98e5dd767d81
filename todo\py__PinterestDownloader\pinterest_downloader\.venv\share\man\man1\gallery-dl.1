.TH "GALLERY-DL" "1" "2025-05-23" "1.29.7" "gallery-dl Manual"
.\" disable hyphenation
.nh

.SH NAME
gallery-dl \- download image-galleries and -collections

.SH SYNOPSIS
.B gallery-dl
[OPTION]... URL...

.SH DESCRIPTION
.B gallery-dl
is a command-line program to download image-galleries and -collections
from several image hosting sites. It is a cross-platform tool
with many configuration options and powerful filenaming capabilities.

.SH OPTIONS
.TP
.B "\-h, \-\-help" 
Print this help message and exit
.TP
.B "\-\-version" 
Print program version and exit
.TP
.B "\-f, \-\-filename" \f[I]FORMAT\f[]
Filename format string for downloaded files ('/O' for "original" filenames)
.TP
.B "\-d, \-\-destination" \f[I]PATH\f[]
Target location for file downloads
.TP
.B "\-D, \-\-directory" \f[I]PATH\f[]
Exact location for file downloads
.TP
.B "\-X, \-\-extractors" \f[I]PATH\f[]
Load external extractors from PATH
.TP
.B "\-\-user\-agent" \f[I]UA\f[]
User-Agent request header
.TP
.B "\-\-clear\-cache" \f[I]MODULE\f[]
Delete cached login sessions, cookies, etc. for MODULE (ALL to delete everything)
.TP
.B "\-U, \-\-update\-check" 
Check if a newer version is available
.TP
.B "\-i, \-\-input\-file" \f[I]FILE\f[]
Download URLs found in FILE ('-' for stdin). More than one --input-file can be specified
.TP
.B "\-I, \-\-input\-file\-comment" \f[I]FILE\f[]
Download URLs found in FILE. Comment them out after they were downloaded successfully.
.TP
.B "\-x, \-\-input\-file\-delete" \f[I]FILE\f[]
Download URLs found in FILE. Delete them after they were downloaded successfully.
.TP
.B "\-\-no\-input" 
Do not prompt for passwords/tokens
.TP
.B "\-q, \-\-quiet" 
Activate quiet mode
.TP
.B "\-w, \-\-warning" 
Print only warnings and errors
.TP
.B "\-v, \-\-verbose" 
Print various debugging information
.TP
.B "\-g, \-\-get\-urls" 
Print URLs instead of downloading
.TP
.B "\-G, \-\-resolve\-urls" 
Print URLs instead of downloading; resolve intermediary URLs
.TP
.B "\-j, \-\-dump\-json" 
Print JSON information
.TP
.B "\-J, \-\-resolve\-json" 
Print JSON information; resolve intermediary URLs
.TP
.B "\-s, \-\-simulate" 
Simulate data extraction; do not download anything
.TP
.B "\-E, \-\-extractor\-info" 
Print extractor defaults and settings
.TP
.B "\-K, \-\-list\-keywords" 
Print a list of available keywords and example values for the given URLs
.TP
.B "\-e, \-\-error\-file" \f[I]FILE\f[]
Add input URLs which returned an error to FILE
.TP
.B "\-N, \-\-print" \f[I][EVENT:]FORMAT\f[]
Write FORMAT during EVENT (default 'prepare') to standard output. Examples: 'id' or 'post:{md5[:8]}'
.TP
.B "\-\-print\-to\-file" \f[I][EVENT:]FORMAT FILE\f[]
Append FORMAT during EVENT to FILE
.TP
.B "\-\-list\-modules" 
Print a list of available extractor modules
.TP
.B "\-\-list\-extractors" \f[I][CATEGORIES]\f[]
Print a list of extractor classes with description, (sub)category and example URL
.TP
.B "\-\-write\-log" \f[I]FILE\f[]
Write logging output to FILE
.TP
.B "\-\-write\-unsupported" \f[I]FILE\f[]
Write URLs, which get emitted by other extractors but cannot be handled, to FILE
.TP
.B "\-\-write\-pages" 
Write downloaded intermediary pages to files in the current directory to debug problems
.TP
.B "\-\-print\-traffic" 
Display sent and read HTTP traffic
.TP
.B "\-\-no\-colors" 
Do not emit ANSI color codes in output
.TP
.B "\-R, \-\-retries" \f[I]N\f[]
Maximum number of retries for failed HTTP requests or -1 for infinite retries (default: 4)
.TP
.B "\-\-http\-timeout" \f[I]SECONDS\f[]
Timeout for HTTP connections (default: 30.0)
.TP
.B "\-\-proxy" \f[I]URL\f[]
Use the specified proxy
.TP
.B "\-\-source\-address" \f[I]IP\f[]
Client-side IP address to bind to
.TP
.B "\-4, \-\-force\-ipv4" 
Make all connections via IPv4
.TP
.B "\-6, \-\-force\-ipv6" 
Make all connections via IPv6
.TP
.B "\-\-no\-check\-certificate" 
Disable HTTPS certificate validation
.TP
.B "\-r, \-\-limit\-rate" \f[I]RATE\f[]
Maximum download rate (e.g. 500k or 2.5M)
.TP
.B "\-\-chunk\-size" \f[I]SIZE\f[]
Size of in-memory data chunks (default: 32k)
.TP
.B "\-\-sleep" \f[I]SECONDS\f[]
Number of seconds to wait before each download. This can be either a constant value or a range (e.g. 2.7 or 2.0-3.5)
.TP
.B "\-\-sleep\-request" \f[I]SECONDS\f[]
Number of seconds to wait between HTTP requests during data extraction
.TP
.B "\-\-sleep\-extractor" \f[I]SECONDS\f[]
Number of seconds to wait before starting data extraction for an input URL
.TP
.B "\-\-no\-part" 
Do not use .part files
.TP
.B "\-\-no\-skip" 
Do not skip downloads; overwrite existing files
.TP
.B "\-\-no\-mtime" 
Do not set file modification times according to Last-Modified HTTP response headers
.TP
.B "\-\-no\-download" 
Do not download any files
.TP
.B "\-o, \-\-option" \f[I]KEY=VALUE\f[]
Additional options. Example: -o browser=firefox
.TP
.B "\-c, \-\-config" \f[I]FILE\f[]
Additional configuration files
.TP
.B "\-\-config\-yaml" \f[I]FILE\f[]
Additional configuration files in YAML format
.TP
.B "\-\-config\-toml" \f[I]FILE\f[]
Additional configuration files in TOML format
.TP
.B "\-\-config\-create" 
Create a basic configuration file
.TP
.B "\-\-config\-status" 
Show configuration file status
.TP
.B "\-\-config\-open" 
Open configuration file in external application
.TP
.B "\-\-config\-ignore" 
Do not read default configuration files
.TP
.B "\-u, \-\-username" \f[I]USER\f[]
Username to login with
.TP
.B "\-p, \-\-password" \f[I]PASS\f[]
Password belonging to the given username
.TP
.B "\-\-netrc" 
Enable .netrc authentication data
.TP
.B "\-C, \-\-cookies" \f[I]FILE\f[]
File to load additional cookies from
.TP
.B "\-\-cookies\-export" \f[I]FILE\f[]
Export session cookies to FILE
.TP
.B "\-\-cookies\-from\-browser" \f[I]BROWSER[/DOMAIN][+KEYRING][:PROFILE][::CONTAINER]\f[]
Name of the browser to load cookies from, with optional domain prefixed with '/', keyring name prefixed with '+', profile prefixed with ':', and container prefixed with '::' ('none' for no container (default), 'all' for all containers)
.TP
.B "\-A, \-\-abort" \f[I]N\f[]
Stop current extractor run after N consecutive file downloads were skipped
.TP
.B "\-T, \-\-terminate" \f[I]N\f[]
Stop current and parent extractor runs after N consecutive file downloads were skipped
.TP
.B "\-\-filesize\-min" \f[I]SIZE\f[]
Do not download files smaller than SIZE (e.g. 500k or 2.5M)
.TP
.B "\-\-filesize\-max" \f[I]SIZE\f[]
Do not download files larger than SIZE (e.g. 500k or 2.5M)
.TP
.B "\-\-download\-archive" \f[I]FILE\f[]
Record all downloaded or skipped files in FILE and skip downloading any file already in it
.TP
.B "\-\-range" \f[I]RANGE\f[]
Index range(s) specifying which files to download. These can be either a constant value, range, or slice (e.g. '5', '8-20', or '1:24:3')
.TP
.B "\-\-chapter\-range" \f[I]RANGE\f[]
Like '--range', but applies to manga chapters and other delegated URLs
.TP
.B "\-\-filter" \f[I]EXPR\f[]
Python expression controlling which files to download. Files for which the expression evaluates to False are ignored. Available keys are the filename-specific ones listed by '-K'. Example: --filter "image_width >= 1000 and rating in ('s', 'q')"
.TP
.B "\-\-chapter\-filter" \f[I]EXPR\f[]
Like '--filter', but applies to manga chapters and other delegated URLs
.TP
.B "\-P, \-\-postprocessor" \f[I]NAME\f[]
Activate the specified post processor
.TP
.B "\-\-no\-postprocessors" 
Do not run any post processors
.TP
.B "\-O, \-\-postprocessor\-option" \f[I]KEY=VALUE\f[]
Additional post processor options
.TP
.B "\-\-write\-metadata" 
Write metadata to separate JSON files
.TP
.B "\-\-write\-info\-json" 
Write gallery metadata to a info.json file
.TP
.B "\-\-write\-tags" 
Write image tags to separate text files
.TP
.B "\-\-zip" 
Store downloaded files in a ZIP archive
.TP
.B "\-\-cbz" 
Store downloaded files in a CBZ archive
.TP
.B "\-\-mtime" \f[I]NAME\f[]
Set file modification times according to metadata selected by NAME. Examples: 'date' or 'status[date]'
.TP
.B "\-\-rename" \f[I]FORMAT\f[]
Rename previously downloaded files from FORMAT to the current filename format
.TP
.B "\-\-rename\-to" \f[I]FORMAT\f[]
Rename previously downloaded files from the current filename format to FORMAT
.TP
.B "\-\-ugoira" \f[I]FMT\f[]
Convert Pixiv Ugoira to FMT using FFmpeg. Supported formats are 'webm', 'mp4', 'gif', 'vp8', 'vp9', 'vp9-lossless', 'copy', 'zip'.
.TP
.B "\-\-exec" \f[I]CMD\f[]
Execute CMD for each downloaded file. Supported replacement fields are {} or {_path}, {_directory}, {_filename}. Example: --exec "convert {} {}.png && rm {}"
.TP
.B "\-\-exec\-after" \f[I]CMD\f[]
Execute CMD after all files were downloaded. Example: --exec-after "cd {_directory} && convert * ../doc.pdf"

.SH EXAMPLES
.TP
gallery-dl \f[I]URL\f[]
Download images from \f[I]URL\f[].
.TP
gallery-dl -g -u <username> -p <password> \f[I]URL\f[]
Print direct URLs from a site that requires authentication.
.TP
gallery-dl --filter 'type == "ugoira"' --range '2-4' \f[I]URL\f[]
Apply filter and range expressions. This will only download
the second, third, and fourth file where its type value is equal to "ugoira".
.TP
gallery-dl r:\f[I]URL\f[]
Scan \f[I]URL\f[] for other URLs and invoke \f[B]gallery-dl\f[] on them.
.TP
gallery-dl oauth:\f[I]SITE\-NAME\f[]
Gain OAuth authentication tokens for
.IR deviantart ,
.IR flickr ,
.IR reddit ,
.IR smugmug ", and"
.IR tumblr .

.SH FILES
.TP
.I /etc/gallery-dl.conf
The system wide configuration file.
.TP
.I ~/.config/gallery-dl/config.json
Per user configuration file.
.TP
.I ~/.gallery-dl.conf
Alternate per user configuration file.

.SH BUGS
https://github.com/mikf/gallery-dl/issues

.SH AUTHORS
Mike Fährmann <<EMAIL>>
.br
and https://github.com/mikf/gallery-dl/graphs/contributors

.SH "SEE ALSO"
.BR gallery-dl.conf (5)
