[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "speech-to-text"
version = "1.0.0"
description = "Interactive speech-to-text transcription tool using OpenAI Whisper"
requires-python = ">=3.9"

dependencies = [
    "torch==2.7.1",
    "numpy>=1.26.4",
    "librosa>=0.10.2",
    "soundfile>=0.12.1",
    "rich>=13.9.4",
    "loguru>=0.7.2",
    "tqdm>=4.67.1",
    "PyYAML>=6.0.2",
    "tiktoken>=0.8.0",
    "transformers>=4.47.1",
    "openai-whisper==20250625",
]

[project.optional-dependencies]
dev = ["pytest>=7.0.0", "black>=23.0.0", "isort>=5.12.0"]

[project.scripts]
speech-to-text = "src.main:main"

[tool.hatch.build.targets.wheel]
packages = ["src"]

[tool.black]
line-length = 88
target-version = ['py39']
