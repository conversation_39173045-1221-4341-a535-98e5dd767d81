import unittest
import tempfile
import shutil
from pathlib import Path
from src.main import (
    folder_from_html,
    folder_from_json,
    bookmarks_urls_parser,
    create_html_from_folder,
    folder_to_dict,
    create_urls_from_folder,
)

class TestBookmarkConversion(unittest.TestCase):
    def setUp(self):
        # Create a temporary directory for test files
        self.test_dir = Path(tempfile.mkdtemp())
        
        # Sample bookmark data with known timestamps and order
        self.test_html = '''<!DOCTYPE NETSCAPE-Bookmark-file-1>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=UTF-8">
<TITLE>Bookmarks</TITLE>
<H1>Bookmarks</H1>
<DL><p>
    <DT><H3 ADD_DATE="1708002901" LAST_MODIFIED="1708002901" PERSONAL_TOOLBAR_FOLDER="true">Bookmarks bar</H3>
    <DL><p>
        <DT><H3 ADD_DATE="1720975562" LAST_MODIFIED="1720975570">Folder1</H3>
        <DL><p>
            <DT><A HREF="https://example1.com" ADD_DATE="1721854212" LAST_MODIFIED="1721854212">Link1</A>
            <DT><A HREF="https://example2.com" ADD_DATE="1720975570" LAST_MODIFIED="1720975570">Link2</A>
        </DL><p>
        <DT><A HREF="https://example3.com" ADD_DATE="1708002901" LAST_MODIFIED="1708002901">.link3</A>
    </DL><p>
</DL><p>'''

    def tearDown(self):
        # Clean up temporary directory
        shutil.rmtree(self.test_dir)

    def test_timestamp_preservation_html_to_folder(self):
        """Test that timestamps are preserved when converting from HTML to folder structure"""
        # Parse HTML
        root_folder = folder_from_html(self.test_html)
        
        # Create URL structure
        urls_dir = self.test_dir / "bookmarks.URLS"
        create_urls_from_folder(root_folder, urls_dir)
        
        # Parse back from URLs
        parsed_folder = bookmarks_urls_parser(urls_dir)
        
        # Verify timestamps
        bookmarks_bar = next(f for f in parsed_folder.subfolders if f.name == "Bookmarks bar")
        self.assertEqual(bookmarks_bar.add_date, 1708002901)
        self.assertEqual(bookmarks_bar.last_modified, 1708002901)
        
        folder1 = next(f for f in bookmarks_bar.subfolders if f.name == "Folder1")
        self.assertEqual(folder1.add_date, 1720975562)
        self.assertEqual(folder1.last_modified, 1720975570)

    def test_order_preservation_html_to_folder(self):
        """Test that order is preserved when converting from HTML to folder structure"""
        # Parse HTML
        root_folder = folder_from_html(self.test_html)
        
        # Verify initial order
        bookmarks_bar = next(f for f in root_folder.subfolders if f.name == "Bookmarks bar")
        
        # Check folder order
        self.assertEqual(bookmarks_bar.subfolders[0].name, "Folder1")
        
        # Check bookmark order in Folder1
        folder1 = bookmarks_bar.subfolders[0]
        self.assertEqual(folder1.bookmarks[0].title, "Link1")
        self.assertEqual(folder1.bookmarks[1].title, "Link2")
        
        # Create URL structure and parse back
        urls_dir = self.test_dir / "bookmarks.URLS"
        create_urls_from_folder(root_folder, urls_dir)
        parsed_folder = bookmarks_urls_parser(urls_dir)
        
        # Verify order is preserved
        parsed_bar = next(f for f in parsed_folder.subfolders if f.name == "Bookmarks bar")
        parsed_folder1 = parsed_bar.subfolders[0]
        
        self.assertEqual(parsed_folder1.bookmarks[0].title, "Link1")
        self.assertEqual(parsed_folder1.bookmarks[1].title, "Link2")

    def test_json_conversion(self):
        """Test that conversion to/from JSON preserves data"""
        # Parse HTML to get initial structure
        root_folder = folder_from_html(self.test_html)
        
        # Convert to JSON dict
        json_dict = folder_to_dict(root_folder)
        
        # Parse back from JSON
        parsed_folder = folder_from_json(json_dict)
        
        # Verify structure
        bookmarks_bar = next(f for f in parsed_folder.subfolders if f.name == "Bookmarks bar")
        folder1 = next(f for f in bookmarks_bar.subfolders if f.name == "Folder1")
        
        # Check timestamps
        self.assertEqual(bookmarks_bar.add_date, 1708002901)
        self.assertEqual(folder1.add_date, 1720975562)
        
        # Check order
        self.assertEqual(folder1.bookmarks[0].title, "Link1")
        self.assertEqual(folder1.bookmarks[1].title, "Link2")

    def test_round_trip_conversion(self):
        """Test complete round-trip conversion: HTML -> URLs -> HTML"""
        # Parse initial HTML
        root_folder = folder_from_html(self.test_html)
        
        # Convert to URLs
        urls_dir = self.test_dir / "bookmarks.URLS"
        create_urls_from_folder(root_folder, urls_dir)
        
        # Parse URLs back to folder
        parsed_folder = bookmarks_urls_parser(urls_dir)
        
        # Convert back to HTML
        final_html = create_html_from_folder(parsed_folder)
        
        # Parse the final HTML
        final_folder = folder_from_html(final_html)
        
        # Verify key structures are preserved
        def verify_folder(original, final):
            self.assertEqual(original.name, final.name)
            self.assertEqual(original.add_date, final.add_date)
            self.assertEqual(original.last_modified, final.last_modified)
            
            # Verify bookmarks
            self.assertEqual(len(original.bookmarks), len(final.bookmarks))
            for orig_bm, final_bm in zip(original.bookmarks, final.bookmarks):
                self.assertEqual(orig_bm.title, final_bm.title)
                self.assertEqual(orig_bm.url, final_bm.url)
                self.assertEqual(orig_bm.add_date, final_bm.add_date)
                self.assertEqual(orig_bm.last_modified, final_bm.last_modified)
                self.assertEqual(orig_bm.order, final_bm.order)
            
            # Verify subfolders recursively
            self.assertEqual(len(original.subfolders), len(final.subfolders))
            for orig_sub, final_sub in zip(original.subfolders, final.subfolders):
                verify_folder(orig_sub, final_sub)
        
        verify_folder(root_folder, final_folder)

class TestBookmarkEdgeCases(unittest.TestCase):
    def setUp(self):
        self.test_dir = Path(tempfile.mkdtemp())
        
        # Test case with None/missing timestamps and mixed types
        self.edge_case_html = '''<!DOCTYPE NETSCAPE-Bookmark-file-1>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=UTF-8">
<TITLE>Bookmarks</TITLE>
<H1>Bookmarks</H1>
<DL><p>
    <DT><H3 ADD_DATE="1732348344" order="0">Folder with missing last_modified</H3>
    <DL><p>
        <DT><A HREF="https://example1.com" ADD_DATE="1732348344" order="0">Link with matching timestamps</A>
        <DT><A HREF="https://example2.com" order="1">Link with no timestamps</A>
        <DT><A HREF="https://example3.com" LAST_MODIFIED="1730627333" order="2">Link with only last_modified</A>
    </DL><p>
    <DT><H3 ADD_DATE="1726745421" LAST_MODIFIED="1726745421" order="1">Workflow</H3>
    <DL><p>
        <DT><H3 ADD_DATE="1726745421" LAST_MODIFIED="1726745421" order="0">Nested Folder</H3>
        <DL><p>
            <DT><A HREF="https://example4.com" ADD_DATE="1726745421" LAST_MODIFIED="1726745421" order="0">Nested Link</A>
        </DL><p>
    </DL><p>
    <DT><H3 ADD_DATE="1726745421" LAST_MODIFIED="1726745421" order="2">Special Characters</H3>
    <DL><p>
        <DT><A HREF="https://example.com/test?q=1&amp;r=2" ADD_DATE="1726745421" LAST_MODIFIED="1726745421" order="0">Link with &amp; and ?</A>
        <DT><A HREF="https://example.com/path/" ADD_DATE="1726745421" LAST_MODIFIED="1726745421" order="1">Link with / and :</A>
        <DT><A HREF="https://example.com/test.html" ADD_DATE="1726745421" LAST_MODIFIED="1726745421" order="2">Link with .html extension</A>
    </DL><p>
</DL><p>'''

    def tearDown(self):
        shutil.rmtree(self.test_dir)

    def test_timestamp_handling(self):
        """Test handling of missing or None timestamps"""
        # Parse HTML
        root_folder = folder_from_html(self.edge_case_html)
        
        # Convert to URLs
        urls_dir = self.test_dir / "edge_case.URLS"
        create_urls_from_folder(root_folder, urls_dir)
        
        # Parse back from URLs
        parsed_folder = bookmarks_urls_parser(urls_dir)
        
        # Test folder with missing last_modified
        folder = next(f for f in parsed_folder.subfolders if f.name == "Folder with missing last_modified")
        self.assertEqual(folder.add_date, 1732348344)
        # When last_modified is missing, it should use add_date
        self.assertEqual(folder.last_modified, 1732348344)
        
        # Test bookmarks with various timestamp combinations
        bookmarks = sorted(folder.bookmarks, key=lambda x: x.order)
        
        # Link with matching timestamps
        self.assertEqual(bookmarks[0].add_date, 1732348344)
        self.assertEqual(bookmarks[0].last_modified, 1732348344)
        
        # Link with no timestamps should have default timestamps
        self.assertEqual(bookmarks[1].add_date, 0)
        self.assertEqual(bookmarks[1].last_modified, 0)
        
        # Link with only last_modified
        self.assertEqual(bookmarks[2].add_date, 0)
        self.assertEqual(bookmarks[2].last_modified, 1730627333)

    def test_folder_structure_preservation(self):
        """Test preservation of nested folder structure"""
        # Parse HTML
        root_folder = folder_from_html(self.edge_case_html)
        
        # Convert to URLs
        urls_dir = self.test_dir / "edge_case.URLS"
        create_urls_from_folder(root_folder, urls_dir)
        
        # Parse back from URLs
        parsed_folder = bookmarks_urls_parser(urls_dir)
        
        # Find Workflow folder (should be second folder by order)
        subfolders = sorted(parsed_folder.subfolders, key=lambda x: x.order)
        workflow = subfolders[1]  # Should be index 1 due to order="1"
        self.assertEqual(workflow.name, "Workflow")
        
        # Verify nested structure
        self.assertEqual(len(workflow.subfolders), 1)
        nested = workflow.subfolders[0]
        self.assertEqual(nested.name, "Nested Folder")
        self.assertEqual(len(nested.bookmarks), 1)
        self.assertEqual(nested.bookmarks[0].title, "Nested Link")
        
        # Verify timestamps are preserved in nested structure
        self.assertEqual(workflow.add_date, 1726745421)
        self.assertEqual(workflow.last_modified, 1726745421)
        self.assertEqual(nested.add_date, 1726745421)
        self.assertEqual(nested.last_modified, 1726745421)
        self.assertEqual(nested.bookmarks[0].add_date, 1726745421)
        self.assertEqual(nested.bookmarks[0].last_modified, 1726745421)

    def test_special_characters(self):
        """Test handling of special characters in titles and URLs"""
        # Parse HTML
        root_folder = folder_from_html(self.edge_case_html)
        
        # Convert to URLs
        urls_dir = self.test_dir / "edge_case.URLS"
        create_urls_from_folder(root_folder, urls_dir)
        
        # Parse back from URLs
        parsed_folder = bookmarks_urls_parser(urls_dir)
        
        # Find Special Characters folder (should be third folder by order)
        subfolders = sorted(parsed_folder.subfolders, key=lambda x: x.order)
        special = subfolders[2]  # Should be index 2 due to order="2"
        self.assertEqual(special.name, "Special Characters")
        
        # Sort bookmarks by order
        bookmarks = sorted(special.bookmarks, key=lambda x: x.order)
        
        # Verify special characters in titles and URLs are preserved
        # HTML entities should be preserved in titles but decoded in URLs
        self.assertEqual(bookmarks[0].title, "Link with &amp; and ?")  # Keep as HTML entity in title
        self.assertEqual(bookmarks[0].url, "https://example.com/test?q=1&amp;r=2")  # Keep as HTML entity in URL
        
        self.assertEqual(bookmarks[1].title, "Link with / and :")
        self.assertEqual(bookmarks[1].url, "https://example.com/path/")
        
        self.assertEqual(bookmarks[2].title, "Link with .html extension")
        self.assertEqual(bookmarks[2].url, "https://example.com/test.html")

    def test_html_round_trip_preservation(self):
        """Test that HTML -> URLs -> HTML preserves structure and timestamps"""
        # Parse initial HTML
        root_folder = folder_from_html(self.edge_case_html)
        
        # Convert to URLs
        urls_dir = self.test_dir / "edge_case.URLS"
        create_urls_from_folder(root_folder, urls_dir)
        
        # Parse URLs back to folder
        parsed_folder = bookmarks_urls_parser(urls_dir)
        
        # Convert back to HTML
        final_html = create_html_from_folder(parsed_folder)
        
        # Parse the final HTML
        final_folder = folder_from_html(final_html)
        
        # Compare the original and final structures
        def compare_folders(orig, final):
            # Sort subfolders by order before comparing
            orig_subs = sorted(orig.subfolders, key=lambda x: x.order)
            final_subs = sorted(final.subfolders, key=lambda x: x.order)
            
            # Compare folder attributes
            self.assertEqual(len(orig_subs), len(final_subs))
            for o_sub, f_sub in zip(orig_subs, final_subs):
                self.assertEqual(o_sub.name, f_sub.name)
                self.assertEqual(o_sub.add_date, f_sub.add_date)
                self.assertEqual(o_sub.last_modified, f_sub.last_modified)
                self.assertEqual(o_sub.order, f_sub.order)
                
                # Sort and compare bookmarks
                o_bms = sorted(o_sub.bookmarks, key=lambda x: x.order)
                f_bms = sorted(f_sub.bookmarks, key=lambda x: x.order)
                self.assertEqual(len(o_bms), len(f_bms))
                for o_bm, f_bm in zip(o_bms, f_bms):
                    self.assertEqual(o_bm.title, f_bm.title)
                    self.assertEqual(o_bm.url, f_bm.url)
                    self.assertEqual(o_bm.add_date, f_bm.add_date)
                    self.assertEqual(o_bm.last_modified, f_bm.last_modified)
                    self.assertEqual(o_bm.order, f_bm.order)
                
                # Recursively compare subfolders
                compare_folders(o_sub, f_sub)
        
        compare_folders(root_folder, final_folder)

if __name__ == '__main__':
    unittest.main(verbosity=2) 