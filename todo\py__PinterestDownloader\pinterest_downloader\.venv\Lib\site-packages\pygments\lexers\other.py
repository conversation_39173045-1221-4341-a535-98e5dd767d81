"""
    pygments.lexers.other
    ~~~~~~~~~~~~~~~~~~~~~

    Just export lexer classes previously contained in this module.

    :copyright: Copyright 2006-2025 by the Pygments team, see AUTHORS.
    :license: BSD, see LICENSE for details.
"""

# ruff: noqa: F401
from pygments.lexers.sql import <PERSON>ql<PERSON>ex<PERSON>, MySqlLexer, SqliteConsoleLexer
from pygments.lexers.shell import <PERSON><PERSON><PERSON><PERSON><PERSON>, BashSessionLexer, BatchLexer, \
    TcshLexer
from pygments.lexers.robotframework import RobotFrameworkLexer
from pygments.lexers.testing import <PERSON><PERSON><PERSON><PERSON>exer
from pygments.lexers.esoteric import <PERSON>fu<PERSON><PERSON>exer, BefungeLexer, RedcodeLexer
from pygments.lexers.prolog import LogtalkLexer
from pygments.lexers.snobol import SnobolLexer
from pygments.lexers.rebol import <PERSON><PERSON><PERSON>exer
from pygments.lexers.configs import <PERSON><PERSON><PERSON>g<PERSON><PERSON><PERSON>, Cfengine3Lexer
from pygments.lexers.modeling import Modelica<PERSON>exer
from pygments.lexers.scripting import <PERSON><PERSON><PERSON>ex<PERSON>, MOOCodeLexer, \
    HybrisLexer
from pygments.lexers.graphics import PostScriptLexer, GnuplotLexer, \
    AsymptoteLexer, PovrayLexer
from pygments.lexers.business import ABAPLexer, OpenEdgeLexer, \
    GoodDataCLLexer, MaqlLexer
from pygments.lexers.automation import AutoItLexer, AutohotkeyLexer
from pygments.lexers.dsls import ProtoBufLexer, BroLexer, PuppetLexer, \
    MscgenLexer, VGLLexer
from pygments.lexers.basic import CbmBasicV2Lexer
from pygments.lexers.pawn import SourcePawnLexer, PawnLexer
from pygments.lexers.ecl import ECLLexer
from pygments.lexers.urbi import UrbiscriptLexer
from pygments.lexers.smalltalk import SmalltalkLexer, NewspeakLexer
from pygments.lexers.installers import NSISLexer, RPMSpecLexer
from pygments.lexers.textedit import AwkLexer
from pygments.lexers.smv import NuSMVLexer

__all__ = []
