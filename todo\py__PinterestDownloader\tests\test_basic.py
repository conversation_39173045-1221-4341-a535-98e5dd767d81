#!/usr/bin/env python3
"""
Basic test suite for Pinterest downloader functionality
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from main import is_valid_pinterest_url, get_pinterest_urls_from_clipboard
import pyperclip
from rich.console import Console

console = Console()

def test_url_validation():
    """Test Pinterest URL validation"""
    console.print("[bold blue]🔗 Testing URL Validation[/bold blue]\n")
    
    valid_urls = [
        "https://pinterest.com/pin/123456789/",
        "https://www.pinterest.com/pin/123456789/",
        "https://pin.it/abc123",
        "https://pinterest.com/username/board-name/",
        "https://www.pinterest.com/username/",
        "pinterest.com/pin/123456789/",
        "www.pinterest.com/pin/123456789/"
    ]
    
    invalid_urls = [
        "https://youtube.com/watch?v=123",
        "https://google.com",
        "not-a-url",
        "https://pinterest.com",  # Too short
        ""
    ]
    
    console.print("✅ Testing valid URLs:")
    for url in valid_urls:
        result = is_valid_pinterest_url(url)
        status = "✅ PASS" if result else "❌ FAIL"
        console.print(f"  {status}: {url}")
    
    console.print("\n❌ Testing invalid URLs:")
    for url in invalid_urls:
        result = is_valid_pinterest_url(url)
        status = "✅ PASS" if not result else "❌ FAIL"
        console.print(f"  {status}: {url}")

def test_clipboard_detection():
    """Test clipboard detection functionality"""
    console.print("\n[bold blue]📋 Testing Clipboard Detection[/bold blue]\n")
    
    test_cases = [
        {
            "name": "Single Pinterest URL",
            "content": "https://pinterest.com/pin/123456789/",
            "expected": 1
        },
        {
            "name": "Multiple Pinterest URLs",
            "content": "https://pinterest.com/pin/123456789/ https://pin.it/abc123 https://pinterest.com/username/board/",
            "expected": 3
        },
        {
            "name": "Mixed content with Pinterest URLs",
            "content": """
            Check out these Pinterest pins:
            https://pinterest.com/pin/123456789/
            Some text here
            https://pin.it/abc123
            And this board: https://pinterest.com/username/board/
            """,
            "expected": 3
        },
        {
            "name": "No Pinterest URLs",
            "content": "https://youtube.com/watch?v=123 https://google.com",
            "expected": 0
        },
        {
            "name": "Duplicate URLs",
            "content": "https://pinterest.com/pin/123456789/ https://pinterest.com/pin/123456789/ https://pin.it/abc123",
            "expected": 2
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        console.print(f"[cyan]Test {i}: {test_case['name']}[/cyan]")
        
        # Set clipboard content
        pyperclip.copy(test_case['content'])
        
        # Test the function
        detected_urls = get_pinterest_urls_from_clipboard()
        
        # Check results
        if len(detected_urls) == test_case['expected']:
            console.print(f"  ✅ [green]PASS[/green] - Found {len(detected_urls)} URL(s)")
        else:
            console.print(f"  ❌ [red]FAIL[/red] - Expected {test_case['expected']}, got {len(detected_urls)}")
        
        # Display found URLs
        if detected_urls:
            for j, url in enumerate(detected_urls, 1):
                console.print(f"    {j}. {url}")
        
        console.print()

def test_dependencies():
    """Test if all required dependencies are available"""
    console.print("[bold blue]📦 Testing Dependencies[/bold blue]\n")
    
    dependencies = [
        'rich',
        'loguru', 
        'pyperclip',
        'gallery_dl'
    ]
    
    all_good = True
    for dep in dependencies:
        try:
            __import__(dep)
            console.print(f"✅ {dep} - OK")
        except ImportError:
            console.print(f"❌ {dep} - MISSING")
            all_good = False
    
    return all_good

if __name__ == "__main__":
    console.print("[bold magenta]🎨 Pinterest Downloader Basic Test Suite[/bold magenta]\n")
    
    # Run tests
    test_url_validation()
    test_clipboard_detection()
    deps_ok = test_dependencies()
    
    console.print("\n[bold green]🎉 Basic tests completed![/bold green]")
    
    if deps_ok:
        console.print("All dependencies are available. Pinterest downloader should work correctly.")
    else:
        console.print("Some dependencies are missing. Please install them first.")
    
    input("\nPress Enter to exit...")
