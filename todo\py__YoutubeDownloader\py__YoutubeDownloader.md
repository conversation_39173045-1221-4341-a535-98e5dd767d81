# YouTube Downloader

## Overview
YouTube video/audio downloader with rich CLI interface, concurrent downloads, and progress tracking.

## Features
- **Multiple download formats**: Video (MP4) and audio (MP3) with quality options
- **Clipboard integration**: Auto-detects YouTube URLs from clipboard
- **Concurrent downloads**: Parallel processing for multiple URLs
- **Rich progress tracking**: Real-time download progress with visual indicators
- **Interactive CLI**: User-friendly prompts and selections
- **Structured logging**: Comprehensive logging with automatic cleanup

## Usage
Run `main.bat` to start the interactive downloader or use `main.py` directly with command-line arguments:

```bash
python main.py --prompt
python main.py -i "URL1" "URL2" -op "output/path"
```

## Dependencies
Managed via `pyproject.toml` with uv package manager. See `techstack.md` for complete technology stack details.
